'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../components/providers/AuthProvider';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Mock data for UI development
const mockData = {
  subscription: {
    status: 'active',
    plan: 'Pro Plan',
    amount: 99.99,
    currency: 'usd',
    nextBillingDate: '2023-12-01',
  },
  invoices: [
    { id: 'in_1', date: '2023-11-01', amount: 99.99, status: 'paid', url: '#' },
    { id: 'in_2', date: '2023-10-01', amount: 99.99, status: 'paid', url: '#' },
  ],
  paymentMethods: [
    { id: 'pm_1', type: 'card', last4: '4242', brand: 'Visa', isDefault: true },
    { id: 'pm_2', type: 'card', last4: '1234', brand: 'Mastercard', isDefault: false },
  ],
};

export default function BillingPage() {
  const { profile, loading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('subscription');
  const [billingData, setBillingData] = useState<any>(null);
  const [billingLoading, setBillingLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!authLoading && !profile) {
      window.location.href = '/login';
    }
  }, [profile, authLoading]);

  useEffect(() => {
    async function fetchBillingData() {
      try {
        setBillingLoading(true);

        // Get the current session token
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('Not authenticated');
        }

        const response = await fetch('/api/stripe/billing-data', {
          headers: {
            'Authorization': `Bearer ${session.access_token}`
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch billing data.');
        }
        const data = await response.json();
        setBillingData(data);
        console.log('Billing data received:', data);
      } catch (err: any) {
        setError(err.message);
        // Fallback to mock data on error for UI development
        setBillingData(mockData);
      } finally {
        setBillingLoading(false);
      }
    }

    fetchBillingData();
  }, []);

  if (authLoading || !profile) {
    return <div className="p-6 bg-white rounded-lg shadow-md">Checking authentication...</div>;
  }

  if (billingLoading) {
    return <div className="p-6 bg-white rounded-lg shadow-md">Loading billing data...</div>;
  }

  const renderContent = () => {
    if (authLoading) {
      return <div>Checking authentication...</div>;
    }

    if (error && !billingData) {
      return <div className="text-red-500">Error: {error}</div>;
    }

    if (!billingData) {
      return <div>No billing data found.</div>;
    }

    switch (activeTab) {
      case 'subscription':
        if (!billingData.subscription) {
          return <div>No active subscription found.</div>;
        }

        return (
          <div className="space-y-4 sm:space-y-6">
            <h2 className="mb-4 text-lg font-semibold sm:text-xl">Current Subscription</h2>
            <div className="p-4 space-y-3 rounded-lg bg-gray-50 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <span className="text-sm text-gray-600 sm:text-base">Status:</span>
                <span className="text-sm font-medium text-green-600 capitalize sm:text-base">
                  {billingData.subscription.status}
                </span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <span className="text-sm text-gray-600 sm:text-base">Plan:</span>
                <span className="text-sm font-medium sm:text-base">{billingData.subscription.plan}</span>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <span className="text-sm text-gray-600 sm:text-base">Next Billing Date:</span>
                <span className="text-sm font-medium sm:text-base">
                  {new Date(billingData.subscription.nextBillingDate).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        );
      case 'invoices':
        return (
          <div className="space-y-4 sm:space-y-6">
            <h2 className="mb-4 text-lg font-semibold sm:text-xl">Billing History</h2>
            <div className="space-y-3">
              {billingData.invoices.map((invoice: any) => (
                <div key={invoice.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                  <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div className="flex flex-col space-y-1 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
                      <span className="text-sm font-medium sm:text-base">
                        {new Date(invoice.date).toLocaleDateString()}
                      </span>
                      <span className="text-sm text-gray-600 sm:text-base">
                        ${invoice.amount.toFixed(2)}
                      </span>
                    </div>
                    <a
                      href={invoice.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm font-medium text-blue-500 hover:underline sm:text-base"
                    >
                      View Invoice
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      case 'paymentMethods':
        return (
          <div className="space-y-4 sm:space-y-6">
            <h2 className="mb-4 text-lg font-semibold sm:text-xl">Payment Methods</h2>
            <div className="space-y-3">
              {billingData.paymentMethods.map((pm: any) => (
                <div key={pm.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                  <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div className="flex flex-col space-y-1 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
                      <span className="text-sm font-medium sm:text-base">
                        {pm.brand} ending in {pm.last4}
                      </span>
                      {pm.isDefault && (
                        <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full">
                          Default
                        </span>
                      )}
                    </div>
                    <button className="self-start text-sm font-medium text-blue-500 hover:text-blue-700 sm:self-auto">
                      Manage
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 sm:p-6">
        <h1 className="mb-2 text-xl font-bold sm:text-2xl">Billing</h1>
        <div className="text-sm text-gray-700 sm:text-base">
          Logged in as: <span className="font-semibold break-all">{profile.email}</span>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b">
        <nav className="flex overflow-x-auto">
          <button
            onClick={() => setActiveTab('subscription')}
            className={`whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 ${
              activeTab === 'subscription'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Subscription
          </button>
          <button
            onClick={() => setActiveTab('invoices')}
            className={`whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 ${
              activeTab === 'invoices'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Invoices
          </button>
          <button
            onClick={() => setActiveTab('paymentMethods')}
            className={`whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 ${
              activeTab === 'paymentMethods'
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <span className="hidden sm:inline">Payment Methods</span>
            <span className="sm:hidden">Payment</span>
          </button>
        </nav>
      </div>

      {/* Content */}
      <div className="p-4 sm:p-6">
        {renderContent()}
      </div>
    </div>
  );
}
