import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const { domain, price, siteId } = await request.json();
    if (!domain || !price) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Get the authorization header to identify the user
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    const session = await stripe.checkout.sessions.create({
      mode: 'payment',
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'aud',
            product_data: {
              name: `Domain Registration: ${domain}`,
              description: `Register domain ${domain} for 1 year`,
            },
            unit_amount: Math.round(price * 100), // Stripe expects cents
          },
          quantity: 1,
        },
      ],
      metadata: {
        domain,
        siteId: siteId || 'pending',
        userId: user.id,
        userEmail: user.email,
      },
      success_url: `${baseUrl}/dashboard/domain/complete?session_id={CHECKOUT_SESSION_ID}&domain=${encodeURIComponent(domain)}&siteId=${encodeURIComponent(siteId || 'pending')}`,
      cancel_url: `${baseUrl}/dashboard/domain`,
    });

    return NextResponse.json({ url: session.url });
  } catch (error: any) {
    console.error('Error creating domain checkout session:', error);
    return NextResponse.json({ error: 'Failed to create domain checkout session' }, { status: 500 });
  }
} 