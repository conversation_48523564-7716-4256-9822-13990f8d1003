import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { getOrCreateStripeCustomer, getStripeCustomerIdFromProfile } from '@/lib/stripe-customer';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-06-30.basil',
  typescript: true,
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// Expects: Authorization: Bearer <supabase_jwt>
export async function GET(req: NextRequest) {
  try {
    // 1. Get the Supabase JWT from the request
    const authHeader = req.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // 2. Get the user from the JWT
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    // 3. Get the Stripe customer ID from profiles table, create if doesn't exist
    let stripeCustomerId = await getStripeCustomerIdFromProfile(user.email!);

    if (!stripeCustomerId) {
      // Create a new Stripe customer if one doesn't exist
      try {
        stripeCustomerId = await getOrCreateStripeCustomer(user.id, user.email!);
      } catch (error) {
        console.error('Error creating Stripe customer:', error);
        return NextResponse.json({ error: 'Failed to create Stripe customer' }, { status: 500 });
      }
    }

    // 4. Fetch subscriptions, invoices, and payment methods from Stripe
    const [subscriptions, invoices, paymentMethods] = await Promise.all([
      stripe.subscriptions.list({
        customer: stripeCustomerId,
        limit: 1,
        expand: ['data.items.data.price.product'],
      }),
      stripe.invoices.list({ customer: stripeCustomerId, limit: 10 }),
      stripe.paymentMethods.list({ customer: stripeCustomerId, type: 'card' }),
    ]);

    // Format the data to send to the frontend
    const responseData = {
      subscription: subscriptions.data[0] ? {
        status: subscriptions.data[0].status,
        plan:
          subscriptions.data[0].items.data[0]?.price.nickname ||
          (subscriptions.data[0].items.data[0]?.price.product as Stripe.Product)?.name ||
          'N/A',
        amount: subscriptions.data[0].items.data[0]?.price.unit_amount / 100,
        currency: subscriptions.data[0].items.data[0]?.price.currency,
        nextBillingDate: subscriptions.data[0].items.data[0]?.current_period_end
          ? new Date(subscriptions.data[0].items.data[0].current_period_end * 1000).toISOString()
          : null,
      } : null,
      invoices: invoices.data.map(invoice => ({
        id: invoice.id,
        date: new Date(invoice.created * 1000).toISOString(),
        amount: invoice.amount_paid / 100,
        status: invoice.status,
        url: invoice.invoice_pdf,
      })),
      paymentMethods: paymentMethods.data.map(pm => ({
        id: pm.id,
        type: pm.type,
        last4: pm.card?.last4,
        brand: pm.card?.brand,
        isDefault: false, // Stripe API doesn't directly flag a default PM in this list view
      })),
    };

    return NextResponse.json(responseData);

  } catch (error: any) {
    console.error('Stripe API Error:', error.message);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
