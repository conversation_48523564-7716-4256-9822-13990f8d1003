import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface Domain {
  id: string;
  domain_name: string;
  user_id: string;
  site_id: string | null;
  status: 'pending' | 'registered' | 'active' | 'expired' | 'failed';
  registration_date: string | null;
  expiry_date: string | null;
  auto_renew: boolean;
  price_paid: number | null;
  currency: string;
  namecheap_order_id: string | null;
  namecheap_transaction_id: string | null;
  stripe_session_id: string | null;
  dns_configured: boolean;
  cname_target: string | null;
  created_at: string;
  updated_at: string;
  site_name?: string; // From joined user-websites table
}

// GET /api/domains - Fetch all domains for the authenticated user
export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    // Fetch domains for the user with optional site information
    const { data: domains, error } = await supabaseAdmin
      .from('domains')
      .select(`
        *,
        user-websites!site_id (
          site_name
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching domains:', error);
      return NextResponse.json({ error: 'Failed to fetch domains' }, { status: 500 });
    }

    // Transform the data to include site_name at the top level
    const transformedDomains = domains?.map(domain => ({
      ...domain,
      site_name: domain['user-websites']?.site_name || null,
      'user-websites': undefined // Remove the nested object
    })) || [];

    return NextResponse.json({ domains: transformedDomains });
  } catch (error: any) {
    console.error('Domains API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/domains - Create a new domain record (used during registration process)
export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const body = await request.json();
    const {
      domain_name,
      site_id,
      status = 'pending',
      price_paid,
      currency = 'AUD',
      stripe_session_id,
      namecheap_order_id,
      namecheap_transaction_id,
      registration_date,
      expiry_date,
      dns_configured = false,
      cname_target
    } = body;

    if (!domain_name) {
      return NextResponse.json({ error: 'Domain name is required' }, { status: 400 });
    }

    // Insert the new domain record
    const { data: domain, error } = await supabaseAdmin
      .from('domains')
      .insert({
        domain_name,
        user_id: user.id,
        site_id,
        status,
        price_paid,
        currency,
        stripe_session_id,
        namecheap_order_id,
        namecheap_transaction_id,
        registration_date,
        expiry_date,
        dns_configured,
        cname_target
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating domain:', error);
      return NextResponse.json({ error: 'Failed to create domain record' }, { status: 500 });
    }

    return NextResponse.json({ domain });
  } catch (error: any) {
    console.error('Domain creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/domains - Update an existing domain record
export async function PATCH(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const body = await request.json();
    const { domain_id, ...updates } = body;

    if (!domain_id) {
      return NextResponse.json({ error: 'Domain ID is required' }, { status: 400 });
    }

    // Update the domain record (RLS will ensure user can only update their own domains)
    const { data: domain, error } = await supabaseAdmin
      .from('domains')
      .update(updates)
      .eq('id', domain_id)
      .eq('user_id', user.id) // Extra security check
      .select()
      .single();

    if (error) {
      console.error('Error updating domain:', error);
      return NextResponse.json({ error: 'Failed to update domain' }, { status: 500 });
    }

    if (!domain) {
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    return NextResponse.json({ domain });
  } catch (error: any) {
    console.error('Domain update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
