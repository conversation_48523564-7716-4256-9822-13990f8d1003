import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-06-30.basil',
});

export async function POST(req: NextRequest) {
  try {
    const { sessionId } = await req.json();
    
    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Retrieve the session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    // Optionally retrieve the subscription details
    let subscription = null;
    if (session.subscription) {
      subscription = await stripe.subscriptions.retrieve(session.subscription as string);
    }

    return NextResponse.json({
      sessionId: session.id,
      paymentStatus: session.payment_status,
      status: session.status,
      customerEmail: session.customer_details?.email,
      amount: session.amount_total ? (session.amount_total / 100) : 0,
      currency: session.currency,
      // Handle both subscription and domain payment metadata
      planName: session.metadata?.planId || session.metadata?.domain || 'Unknown',
      billing: session.metadata?.isYearly === 'true' ? 'Yearly' : 'Monthly',
      // Domain-specific metadata
      domain: session.metadata?.domain,
      siteId: session.metadata?.siteId,
      userId: session.metadata?.userId,
      userEmail: session.metadata?.userEmail,
      subscription: subscription ? {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: subscription.current_period_start,
        currentPeriodEnd: subscription.current_period_end,
      } : null,
    });
  } catch (error: any) {
    console.error('Session verification error:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}