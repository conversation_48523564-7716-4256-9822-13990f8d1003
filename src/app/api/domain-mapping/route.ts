import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Function to set CNAME record via Namecheap API
async function setCNAMERecord(domain: string, cnameValue: string) {
  const apiUser = process.env.NAMECHEAP_API_USER!;
  const apiKey = process.env.NAMECHEAP_API_KEY!;
  const username = process.env.NAMECHEAP_USERNAME!;
  const clientIp = process.env.NAMECHEAP_CLIENT_IP!;
  const sandbox = process.env.NAMECHEAP_SANDBOX === 'true';

  const baseUrl = sandbox 
    ? 'https://api.sandbox.namecheap.com/xml.response'
    : 'https://api.namecheap.com/xml.response';

  const params = new URLSearchParams({
    ApiUser: apiUser,
    ApiKey: apiKey,
    UserName: username,
    Command: 'namecheap.domains.dns.setHosts',
    ClientIp: clientIp,
    SLD: domain.split('.')[0],
    TLD: domain.split('.').slice(1).join('.'),
    HostName1: '@',
    RecordType1: 'CNAME',
    Address1: cnameValue,
    TTL1: '1800'
  });

  const response = await fetch(`${baseUrl}?${params.toString()}`);
  const xmlText = await response.text();
  
  console.log('[CNAME API] Response:', xmlText);
  
  if (xmlText.includes('<Status>OK</Status>')) {
    return { success: true, message: 'CNAME record set successfully' };
  } else {
    const errorMatch = xmlText.match(/<Error[^>]*>([^<]+)<\/Error>/);
    const errorMessage = errorMatch ? errorMatch[1] : 'Unknown error setting CNAME record';
    throw new Error(errorMessage);
  }
}

// POST /api/domain-mapping - Map a domain to a site with CNAME setup
export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get the user from the JWT
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid user' }, { status: 401 });
    }

    const body = await request.json();
    const { domainName, siteId } = body;

    if (!domainName || !siteId) {
      return NextResponse.json({ error: 'Domain name and site ID are required' }, { status: 400 });
    }

    console.log('[Domain Mapping] Starting mapping process:', { domainName, siteId, userId: user.id });

    // 1. Fetch site information
    const { data: site, error: siteError } = await supabaseAdmin
      .from('user-websites')
      .select('site_name, id')
      .eq('id', siteId)
      .eq('user_id', user.id) // Ensure user owns the site
      .single();

    if (siteError || !site) {
      console.error('[Domain Mapping] Site not found:', siteError);
      return NextResponse.json({ error: 'Site not found or access denied' }, { status: 404 });
    }

    const originalSiteName = site.site_name;
    console.log('[Domain Mapping] Found site:', originalSiteName);

    // 2. Verify domain ownership
    const { data: domain, error: domainError } = await supabaseAdmin
      .from('domains')
      .select('id, status')
      .eq('domain_name', domainName)
      .eq('user_id', user.id)
      .single();

    if (domainError || !domain) {
      console.error('[Domain Mapping] Domain not found:', domainError);
      return NextResponse.json({ error: 'Domain not found or access denied' }, { status: 404 });
    }

    console.log('[Domain Mapping] Found domain:', domain);

    // 3. Set up CNAME record
    console.log('[Domain Mapping] Setting CNAME record:', domainName, '->', originalSiteName);
    let cnameResult;
    try {
      cnameResult = await setCNAMERecord(domainName, originalSiteName);
      console.log('[Domain Mapping] CNAME setup successful:', cnameResult);
    } catch (cnameError) {
      console.error('[Domain Mapping] CNAME setup failed:', cnameError);
      return NextResponse.json({ 
        error: `Failed to configure DNS: ${cnameError instanceof Error ? cnameError.message : cnameError}` 
      }, { status: 500 });
    }

    // 4. Update domain record
    const { error: domainUpdateError } = await supabaseAdmin
      .from('domains')
      .update({
        site_id: siteId,
        dns_configured: true,
        status: 'active',
        cname_target: originalSiteName,
        updated_at: new Date().toISOString()
      })
      .eq('id', domain.id);

    if (domainUpdateError) {
      console.error('[Domain Mapping] Failed to update domain record:', domainUpdateError);
      return NextResponse.json({ error: 'Failed to update domain record' }, { status: 500 });
    }

    // 5. Update site name to the new domain
    const { error: siteUpdateError } = await supabaseAdmin
      .from('user-websites')
      .update({
        site_name: domainName,
        updated_at: new Date().toISOString()
      })
      .eq('id', siteId);

    if (siteUpdateError) {
      console.error('[Domain Mapping] Failed to update site name:', siteUpdateError);
      return NextResponse.json({ error: 'Failed to update site name' }, { status: 500 });
    }

    console.log('[Domain Mapping] Mapping completed successfully');

    return NextResponse.json({
      success: true,
      message: 'Domain mapped successfully',
      domain: domainName,
      site: {
        id: siteId,
        oldName: originalSiteName,
        newName: domainName
      },
      cnameResult
    });

  } catch (error: any) {
    console.error('[Domain Mapping] Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
