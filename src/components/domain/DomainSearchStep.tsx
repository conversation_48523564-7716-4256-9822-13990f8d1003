"use client";

import React from 'react';
import { Search, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';
import { useDomainSearch } from '../../hooks/useDomainSearch';

interface DomainCheckResult {
  Domain: string;
  Available: boolean;
  IsPremiumName: boolean;
  Price?: number;
  Error?: string;
}

interface DomainSearchStepProps {
  onDomainSelect: (domain: DomainCheckResult) => void;
  selectedDomain: DomainCheckResult | null;
}

const DomainSearchStep: React.FC<DomainSearchStepProps> = ({
  onDomainSelect,
  selectedDomain,
}) => {
  const {
    searchTerm,
    setSearchTerm,
    results,
    loading,
    error,
    hasSearched,
  } = useDomainSearch({
    debounceMs: 600,
    minLength: 2,
  });

  const handleDomainSelect = (domain: DomainCheckResult) => {
    if (domain.Available && !domain.Error) {
      onDomainSelect(domain);
    }
  };

  const getStatusIcon = (result: DomainCheckResult) => {
    if (result.Error) {
      return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
    if (result.Available) {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
    return <XCircle className="w-5 h-5 text-red-500" />;
  };

  const getStatusText = (result: DomainCheckResult) => {
    if (result.Error) {
      return <span className="text-red-500 text-sm">{result.Error}</span>;
    }
    if (result.Available) {
      return <span className="text-green-600 font-semibold">Available!</span>;
    }
    return <span className="text-red-500 font-semibold">Unavailable</span>;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Find Your Perfect Domain</h2>
        <p className="text-gray-600">
          Start typing to search for available domains. We'll show you real-time availability and pricing.
        </p>
      </div>

      {/* Search Input */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Enter your domain name (e.g., my-awesome-site)"
            className="w-full pl-10 pr-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
          />
        </div>
        
        {/* Loading indicator */}
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <Clock className="w-5 h-5 text-gray-400 animate-spin" />
          </div>
        )}
      </div>

      {/* Search Instructions */}
      {!hasSearched && searchTerm.length === 0 && (
        <div className="text-center py-8">
          <div className="bg-blue-50 rounded-lg p-6 max-w-md mx-auto">
            <Search className="w-12 h-12 text-blue-500 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Ready to Search</h3>
            <p className="text-blue-600 text-sm">
              Type your desired domain name above and we'll check availability across multiple extensions automatically.
            </p>
          </div>
        </div>
      )}

      {/* Search too short message */}
      {searchTerm.length > 0 && searchTerm.length < 2 && (
        <div className="text-center py-4">
          <p className="text-gray-500 text-sm">Type at least 2 characters to start searching...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700 font-medium">Search Error</span>
          </div>
          <p className="text-red-600 text-sm mt-1">{error}</p>
        </div>
      )}

      {/* Results */}
      {results.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-800">Available Domains</h3>
            <span className="text-sm text-gray-500">{results.length} results</span>
          </div>

          <div className="grid gap-3">
            {results.map((result) => (
              <div
                key={result.Domain}
                className={`border rounded-lg p-4 transition-all cursor-pointer ${
                  selectedDomain?.Domain === result.Domain
                    ? 'border-green-500 bg-green-50 ring-2 ring-green-200'
                    : result.Available && !result.Error
                    ? 'border-gray-200 hover:border-green-300 hover:bg-green-50'
                    : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                }`}
                onClick={() => handleDomainSelect(result)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {getStatusIcon(result)}
                    <div className="flex-1 min-w-0">
                      <p className="text-lg font-medium text-gray-800 truncate">
                        {result.Domain}
                      </p>
                      {result.Price && (
                        <p className="text-sm text-gray-600">
                          AUD ${result.Price.toFixed(2)}/year
                          {result.IsPremiumName && (
                            <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full font-medium">
                              Premium
                            </span>
                          )}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    {getStatusText(result)}
                    {result.Available && !result.Error && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDomainSelect(result);
                        }}
                        className={`px-4 py-2 rounded-md font-medium transition-colors ${
                          selectedDomain?.Domain === result.Domain
                            ? 'bg-green-600 text-white'
                            : 'bg-blue-600 hover:bg-blue-700 text-white'
                        }`}
                      >
                        {selectedDomain?.Domain === result.Domain ? 'Selected' : 'Select'}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No results message */}
      {hasSearched && results.length === 0 && !loading && !error && (
        <div className="text-center py-8">
          <div className="bg-gray-50 rounded-lg p-6 max-w-md mx-auto">
            <XCircle className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No Results Found</h3>
            <p className="text-gray-500 text-sm">
              Try a different domain name or check your spelling.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default DomainSearchStep;
