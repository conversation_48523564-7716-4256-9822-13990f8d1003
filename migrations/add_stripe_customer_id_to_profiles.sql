-- Add stripe_customer_id column to profiles table
-- This migration adds a nullable column to store Stripe customer IDs

-- Add the column
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;

-- Add a unique constraint to ensure one-to-one mapping
ALTER TABLE profiles
ADD CONSTRAINT unique_stripe_customer_id
UNIQUE (stripe_customer_id);

-- Add an index for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_stripe_customer_id
ON profiles (stripe_customer_id);

-- Add a comment to document the column
COMMENT ON COLUMN profiles.stripe_customer_id IS 'Stripe customer ID for billing and subscription management';

-- Add user_id column to user-websites table to establish proper relationship with auth.users
ALTER TABLE "user-websites"
ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;

-- Create index for faster user lookups
CREATE INDEX IF NOT EXISTS idx_user_websites_user_id
ON "user-websites" (user_id);
