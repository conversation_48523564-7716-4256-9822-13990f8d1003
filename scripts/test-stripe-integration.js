#!/usr/bin/env node

/**
 * Test script for Stripe-Supabase integration
 * 
 * This script tests the integration between Stripe and Supabase
 * by creating test customers and verifying the database updates.
 * 
 * Usage: node scripts/test-stripe-integration.js
 */

const { createClient } = require('@supabase/supabase-js');
const Stripe = require('stripe');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function testStripeIntegration() {
  console.log('🧪 Testing Stripe-Supabase Integration...\n');

  try {
    // Test 1: Check database schema
    console.log('1️⃣ Checking database schema...');
    const { data: columns, error: schemaError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'profiles')
      .eq('column_name', 'stripe_customer_id');

    if (schemaError) {
      console.error('❌ Schema check failed:', schemaError.message);
      return;
    }

    if (columns && columns.length > 0) {
      console.log('✅ stripe_customer_id column exists');
      console.log(`   Type: ${columns[0].data_type}, Nullable: ${columns[0].is_nullable}\n`);
    } else {
      console.error('❌ stripe_customer_id column not found in profiles table');
      console.log('   Please run the database migration first.\n');
      return;
    }

    // Test 2: Test customer creation utility
    console.log('2️⃣ Testing customer creation utility...');
    
    // Create a test user profile if it doesn't exist
    const testEmail = '<EMAIL>';
    const testUserId = 'test-user-' + Date.now();

    // Insert test profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .upsert({
        email: testEmail,
        first_name: 'Test',
        last_name: 'User',
        phone: '+1234567890',
        address1: '123 Test St',
        city: 'Test City',
        state_province: 'Test State',
        postal_code: '12345',
        country: 'US'
      })
      .select()
      .single();

    if (profileError) {
      console.error('❌ Failed to create test profile:', profileError.message);
      return;
    }

    console.log('✅ Test profile created/updated');

    // Test customer creation
    const { getOrCreateStripeCustomer } = require('../src/lib/stripe-customer');
    
    try {
      const customerId = await getOrCreateStripeCustomer(testUserId, testEmail);
      console.log('✅ Stripe customer created:', customerId);

      // Verify customer exists in Stripe
      const customer = await stripe.customers.retrieve(customerId);
      console.log('✅ Customer verified in Stripe:', customer.email);

      // Verify customer ID is stored in database
      const { data: updatedProfile } = await supabase
        .from('profiles')
        .select('stripe_customer_id')
        .eq('email', testEmail)
        .single();

      if (updatedProfile?.stripe_customer_id === customerId) {
        console.log('✅ Customer ID stored in database\n');
      } else {
        console.error('❌ Customer ID not stored in database\n');
      }

    } catch (error) {
      console.error('❌ Customer creation failed:', error.message);
    }

    // Test 3: Test API endpoints
    console.log('3️⃣ Testing API endpoints...');
    
    // Note: API endpoint testing would require authentication
    // This is a placeholder for manual testing
    console.log('ℹ️  API endpoint testing requires authentication');
    console.log('   Test manually by:');
    console.log('   1. Logging into your app');
    console.log('   2. Going to /payments page');
    console.log('   3. Selecting a plan');
    console.log('   4. Checking that checkout session is created with your customer ID\n');

    // Test 4: Cleanup
    console.log('4️⃣ Cleaning up test data...');
    
    // Delete test customer from Stripe
    try {
      const { data: testProfile } = await supabase
        .from('profiles')
        .select('stripe_customer_id')
        .eq('email', testEmail)
        .single();

      if (testProfile?.stripe_customer_id) {
        await stripe.customers.del(testProfile.stripe_customer_id);
        console.log('✅ Test customer deleted from Stripe');
      }
    } catch (error) {
      console.log('ℹ️  Stripe customer cleanup skipped:', error.message);
    }

    // Delete test profile
    const { error: deleteError } = await supabase
      .from('profiles')
      .delete()
      .eq('email', testEmail);

    if (!deleteError) {
      console.log('✅ Test profile deleted from database');
    } else {
      console.log('ℹ️  Database cleanup skipped:', deleteError.message);
    }

    console.log('\n🎉 Integration test completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Test the payment flow manually in your app');
    console.log('2. Check that customers are created automatically');
    console.log('3. Verify billing data is retrieved correctly');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
if (require.main === module) {
  testStripeIntegration().then(() => {
    process.exit(0);
  }).catch((error) => {
    console.error('Test script failed:', error);
    process.exit(1);
  });
}

module.exports = { testStripeIntegration };
