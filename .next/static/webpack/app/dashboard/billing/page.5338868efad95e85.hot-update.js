"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/billing/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/billing/page.tsx":
/*!********************************************!*\
  !*** ./src/app/dashboard/billing/page.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BillingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/providers/AuthProvider */ \"(app-pages-browser)/./src/components/providers/AuthProvider.tsx\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\n// Mock data for UI development\nconst mockData = {\n    subscription: {\n        status: \"active\",\n        plan: \"Pro Plan\",\n        amount: 99.99,\n        currency: \"usd\",\n        nextBillingDate: \"2023-12-01\"\n    },\n    invoices: [\n        {\n            id: \"in_1\",\n            date: \"2023-11-01\",\n            amount: 99.99,\n            status: \"paid\",\n            url: \"#\"\n        },\n        {\n            id: \"in_2\",\n            date: \"2023-10-01\",\n            amount: 99.99,\n            status: \"paid\",\n            url: \"#\"\n        }\n    ],\n    paymentMethods: [\n        {\n            id: \"pm_1\",\n            type: \"card\",\n            last4: \"4242\",\n            brand: \"Visa\",\n            isDefault: true\n        },\n        {\n            id: \"pm_2\",\n            type: \"card\",\n            last4: \"1234\",\n            brand: \"Mastercard\",\n            isDefault: false\n        }\n    ]\n};\nfunction BillingPage() {\n    _s();\n    const { profile, loading: authLoading } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"subscription\");\n    const [billingData, setBillingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [billingLoading, setBillingLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading && !profile) {\n            window.location.href = \"/login\";\n        }\n    }, [\n        profile,\n        authLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        async function fetchBillingData() {\n            try {\n                setBillingLoading(true);\n                // Get the current session token\n                const { data: { session } } = await supabase.auth.getSession();\n                if (!session) {\n                    throw new Error(\"Not authenticated\");\n                }\n                const response = await fetch(\"/api/stripe/billing-data\", {\n                    headers: {\n                        \"Authorization\": \"Bearer \".concat(session.access_token)\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch billing data.\");\n                }\n                const data = await response.json();\n                setBillingData(data);\n                console.log(\"Billing data received:\", data);\n            } catch (err) {\n                setError(err.message);\n                // Fallback to mock data on error for UI development\n                setBillingData(mockData);\n            } finally{\n                setBillingLoading(false);\n            }\n        }\n        fetchBillingData();\n    }, []);\n    if (authLoading || !profile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 bg-white rounded-lg shadow-md\",\n            children: \"Checking authentication...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n            lineNumber: 80,\n            columnNumber: 12\n        }, this);\n    }\n    if (billingLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 bg-white rounded-lg shadow-md\",\n            children: \"Loading billing data...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n            lineNumber: 84,\n            columnNumber: 12\n        }, this);\n    }\n    const renderContent = ()=>{\n        if (authLoading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Checking authentication...\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 89,\n                columnNumber: 14\n            }, this);\n        }\n        if (error && !billingData) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-red-500\",\n                children: [\n                    \"Error: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 93,\n                columnNumber: 14\n            }, this);\n        }\n        if (!billingData) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"No billing data found.\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 97,\n                columnNumber: 14\n            }, this);\n        }\n        switch(activeTab){\n            case \"subscription\":\n                if (!billingData.subscription) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"No active subscription found.\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 18\n                    }, this);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-lg font-semibold sm:text-xl\",\n                            children: \"Current Subscription\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-3 rounded-lg bg-gray-50 sm:p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 sm:text-base\",\n                                            children: \"Status:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-green-600 capitalize sm:text-base\",\n                                            children: billingData.subscription.status\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 sm:text-base\",\n                                            children: \"Plan:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium sm:text-base\",\n                                            children: billingData.subscription.plan\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 sm:text-base\",\n                                            children: \"Next Billing Date:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium sm:text-base\",\n                                            children: new Date(billingData.subscription.nextBillingDate).toLocaleDateString()\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this);\n            case \"invoices\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-lg font-semibold sm:text-xl\",\n                            children: \"Billing History\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: billingData.invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border border-gray-200 rounded-lg bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-1 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium sm:text-base\",\n                                                        children: new Date(invoice.date).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600 sm:text-base\",\n                                                        children: [\n                                                            \"$\",\n                                                            invoice.amount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: invoice.url,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-sm font-medium text-blue-500 hover:underline sm:text-base\",\n                                                children: \"View Invoice\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 19\n                                    }, this)\n                                }, invoice.id, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 11\n                }, this);\n            case \"paymentMethods\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 sm:space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-4 text-lg font-semibold sm:text-xl\",\n                            children: \"Payment Methods\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: billingData.paymentMethods.map((pm)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border border-gray-200 rounded-lg bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-1 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium sm:text-base\",\n                                                        children: [\n                                                            pm.brand,\n                                                            \" ending in \",\n                                                            pm.last4\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    pm.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full\",\n                                                        children: \"Default\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"self-start text-sm font-medium text-blue-500 hover:text-blue-700 sm:self-auto\",\n                                                children: \"Manage\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 19\n                                    }, this)\n                                }, pm.id, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200 sm:p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"mb-2 text-xl font-bold sm:text-2xl\",\n                        children: \"Billing\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700 sm:text-base\",\n                        children: [\n                            \"Logged in as: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold break-all\",\n                                children: profile.email\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"flex overflow-x-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"subscription\"),\n                            className: \"whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 \".concat(activeTab === \"subscription\" ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: \"Subscription\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"invoices\"),\n                            className: \"whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 \".concat(activeTab === \"invoices\" ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: \"Invoices\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"paymentMethods\"),\n                            className: \"whitespace-nowrap pb-4 px-4 sm:px-6 pt-4 border-b-2 font-medium text-sm sm:text-base flex-shrink-0 \".concat(activeTab === \"paymentMethods\" ? \"border-indigo-500 text-indigo-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"hidden sm:inline\",\n                                    children: \"Payment Methods\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sm:hidden\",\n                                    children: \"Payment\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 sm:p-6\",\n                children: renderContent()\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/billing/page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"N+2M8qz2LhyIHOH8t6q16WK1QqU=\", false, function() {\n    return [\n        _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/billing/page.tsx\n"));

/***/ })

});