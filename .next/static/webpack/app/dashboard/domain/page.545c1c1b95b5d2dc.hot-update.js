"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/domain/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/domain/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/providers/AuthProvider */ \"(app-pages-browser)/./src/components/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst DomainPage = ()=>{\n    var _sites_find;\n    _s();\n    const { user, loading: authLoading } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"my-domains\");\n    const [wizardStep, setWizardStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"search\");\n    const [selectedDomain, setSelectedDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [registeredDomain, setRegisteredDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch sites on component mount\n    useEffect(()=>{\n        if (!authLoading && !user) {\n            window.location.href = \"/login\";\n        }\n    }, [\n        user,\n        authLoading\n    ]);\n    useEffect(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const { data, error } = await supabase.from(\"user-websites\").select(\"id, site_name, expiry_status, expiry_time\");\n                if (error) {\n                    throw error;\n                }\n                setSites(data);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n            } finally{\n                setSitesLoading(false);\n            }\n        };\n        fetchSites();\n    }, [\n        supabase\n    ]);\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        if (!domainName.trim()) return;\n        setLoading(true);\n        setResults([]);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain: domainName.trim(),\n                    action: \"check\"\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to check domain availability.\");\n            }\n            const data = await response.json();\n            setResults(data.results);\n            console.log(\"data.results\", data.results);\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain search error:\", err);\n            setResults([\n                {\n                    Domain: domainName,\n                    Available: false,\n                    IsPremiumName: false,\n                    Error: err.message\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRegister = async (domain)=>{\n        if (!selectedSiteId) {\n            alert(\"Please select a site to associate with this domain.\");\n            return;\n        }\n        setRegistering(domain);\n        // Find the price for the selected domain\n        const domainResult = results.find((r)=>r.Domain === domain);\n        const price = domainResult === null || domainResult === void 0 ? void 0 : domainResult.Price;\n        if (!price) {\n            alert(\"Could not determine the price for this domain.\");\n            setRegistering(null);\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/domain-checkout-session\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain,\n                    price,\n                    siteId: selectedSiteId\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to initiate payment.\");\n            }\n            const { url } = await response.json();\n            if (url) {\n                window.location.href = url;\n            } else {\n                throw new Error(\"No Stripe Checkout URL returned.\");\n            }\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain payment initiation error:\", err);\n            alert(\"Failed to initiate payment for \".concat(domain, \": \").concat(err.message));\n            setRegistering(null);\n        }\n    };\n    if (authLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Checking authentication...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 136,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (sitesLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Loading sites...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 139,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 sm:p-6 lg:p-8 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 sm:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"mb-2 text-2xl font-bold text-gray-800 sm:mb-4 sm:text-3xl lg:text-4xl\",\n                            children: \"Register a Domain\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 sm:text-base\",\n                            children: \"Find and register the perfect domain for your new website.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 mb-6 bg-white rounded-lg shadow-sm sm:p-6 sm:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-3 text-lg font-semibold text-gray-800 sm:mb-4 sm:text-xl\",\n                            children: \"Select Site\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 text-sm text-gray-600 sm:text-base\",\n                            children: \"Choose which site you want to associate with your new domain.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined),\n                        sitesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 sm:text-base\",\n                            children: \"Loading sites...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, undefined) : sites.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 sm:text-base\",\n                            children: \"No sites available. Please create a site first.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedSiteId,\n                            onChange: (e)=>setSelectedSiteId(e.target.value),\n                            className: \"w-full p-2.5 sm:p-3 text-sm sm:text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a site...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined),\n                                sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: site.id,\n                                        children: [\n                                            site.site_name,\n                                            \" (\",\n                                            site.expiry_status,\n                                            \")\"\n                                        ]\n                                    }, site.id, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 mb-6 bg-white rounded-lg shadow-sm sm:p-6 sm:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-3 text-lg font-semibold text-gray-800 sm:mb-4 sm:text-xl\",\n                            children: \"Search for Domain\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 text-sm text-gray-600 sm:text-base\",\n                            children: \"Enter a domain name to check availability across multiple extensions.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        !selectedSiteId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 mb-4 border rounded-md text-amber-700 bg-amber-50 border-amber-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs sm:text-sm\",\n                                children: \"⚠️ Please select a site above before searching for domains.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-stretch gap-3 sm:flex-row sm:items-center sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domainName,\n                                    onChange: (e)=>setDomainName(e.target.value),\n                                    placeholder: \"Find your new domain (e.g., my-awesome-site)\",\n                                    className: \"flex-grow p-2.5 sm:p-3 text-sm sm:text-base transition border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                                    onKeyDown: (e)=>e.key === \"Enter\" && handleSearch(e)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSearch,\n                                    disabled: loading || !domainName.trim() || !selectedSiteId,\n                                    className: \"px-4 sm:px-6 py-2.5 sm:py-3 text-sm sm:text-base font-bold text-white transition bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed whitespace-nowrap\",\n                                    title: !selectedSiteId ? \"Please select a site first\" : \"\",\n                                    children: loading ? \"Searching...\" : \"Search\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, undefined),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-white rounded-lg shadow-sm sm:p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col mb-4 space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800 sm:text-2xl\",\n                                    children: \"Results\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedSiteId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 sm:text-sm\",\n                                    children: [\n                                        \"Will be associated with: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium break-all\",\n                                            children: (_sites_find = sites.find((s)=>s.id === selectedSiteId)) === null || _sites_find === void 0 ? void 0 : _sites_find.site_name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 44\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 sm:space-y-4\",\n                            children: results.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md sm:p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-base font-medium text-gray-700 break-all sm:text-lg\",\n                                                        children: result.Domain\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    result.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 sm:text-sm\",\n                                                        children: [\n                                                            \"AUD $\",\n                                                            result.Price.toFixed(2),\n                                                            \"/year\",\n                                                            result.IsPremiumName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 font-semibold text-orange-500\",\n                                                                children: \"Premium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 52\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4\",\n                                                children: result.Error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-red-500 sm:text-base\",\n                                                    children: result.Error\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 25\n                                                }, undefined) : result.Available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-bold text-green-600 sm:text-base\",\n                                                            children: \"Available!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleRegister(result.Domain),\n                                                            disabled: registering === result.Domain,\n                                                            className: \"px-3 sm:px-4 py-2 text-sm sm:text-base font-bold text-white transition bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed min-w-[100px] whitespace-nowrap\",\n                                                            children: registering === result.Domain ? \"Registering...\" : \"Register\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-red-500 sm:text-base\",\n                                                    children: \"Unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, result.Domain, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainPage, \"Nho3vuyqOWI7jQbvIW9Ki9gUl0I=\", false, function() {\n    return [\n        _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DomainPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainPage);\nvar _c;\n$RefreshReg$(_c, \"DomainPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/domain/page.tsx\n"));

/***/ })

});