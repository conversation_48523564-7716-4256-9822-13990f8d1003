"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/domain/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/domain/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/providers/AuthProvider */ \"(app-pages-browser)/./src/components/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst DomainPage = ()=>{\n    var _sites_find;\n    _s();\n    const { user, loading: authLoading } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"my-domains\");\n    const [wizardStep, setWizardStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"search\");\n    const [selectedDomain, setSelectedDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [registeredDomain, setRegisteredDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle domain selection from search step\n    const handleDomainSelect = (domain)=>{\n        setSelectedDomain(domain);\n        setWizardStep(\"payment\");\n    };\n    // Handle payment initiation\n    const handlePaymentInitiated = ()=>{\n    // Payment step will redirect to Stripe, so no additional action needed here\n    };\n    // Handle going back to search\n    const handleBackToSearch = ()=>{\n        setWizardStep(\"search\");\n        setSelectedDomain(null);\n    };\n    // Handle domain mapping completion\n    const handleMappingComplete = async (siteId)=>{\n        // This would be called after successful payment and domain registration\n        // For now, just redirect to dashboard\n        window.location.href = \"/dashboard\";\n    };\n    // Handle tab switching\n    const handleTabSwitch = (tab)=>{\n        setActiveTab(tab);\n        if (tab === \"register\") {\n            setWizardStep(\"search\");\n            setSelectedDomain(null);\n            setRegisteredDomain(null);\n        }\n    };\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        if (!domainName.trim()) return;\n        setLoading(true);\n        setResults([]);\n        try {\n            const response = await fetch(\"/api/namecheap\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain: domainName.trim(),\n                    action: \"check\"\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to check domain availability.\");\n            }\n            const data = await response.json();\n            setResults(data.results);\n            console.log(\"data.results\", data.results);\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain search error:\", err);\n            setResults([\n                {\n                    Domain: domainName,\n                    Available: false,\n                    IsPremiumName: false,\n                    Error: err.message\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRegister = async (domain)=>{\n        if (!selectedSiteId) {\n            alert(\"Please select a site to associate with this domain.\");\n            return;\n        }\n        setRegistering(domain);\n        // Find the price for the selected domain\n        const domainResult = results.find((r)=>r.Domain === domain);\n        const price = domainResult === null || domainResult === void 0 ? void 0 : domainResult.Price;\n        if (!price) {\n            alert(\"Could not determine the price for this domain.\");\n            setRegistering(null);\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/domain-checkout-session\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    domain,\n                    price,\n                    siteId: selectedSiteId\n                })\n            });\n            if (!response.ok) {\n                const { error } = await response.json();\n                throw new Error(error || \"Failed to initiate payment.\");\n            }\n            const { url } = await response.json();\n            if (url) {\n                window.location.href = url;\n            } else {\n                throw new Error(\"No Stripe Checkout URL returned.\");\n            }\n        } catch (error) {\n            const err = error;\n            console.error(\"Domain payment initiation error:\", err);\n            alert(\"Failed to initiate payment for \".concat(domain, \": \").concat(err.message));\n            setRegistering(null);\n        }\n    };\n    if (authLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Checking authentication...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 141,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (sitesLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Loading sites...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 144,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 sm:p-6 lg:p-8 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 sm:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"mb-2 text-2xl font-bold text-gray-800 sm:mb-4 sm:text-3xl lg:text-4xl\",\n                            children: \"Register a Domain\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 sm:text-base\",\n                            children: \"Find and register the perfect domain for your new website.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 mb-6 bg-white rounded-lg shadow-sm sm:p-6 sm:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-3 text-lg font-semibold text-gray-800 sm:mb-4 sm:text-xl\",\n                            children: \"Select Site\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 text-sm text-gray-600 sm:text-base\",\n                            children: \"Choose which site you want to associate with your new domain.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, undefined),\n                        sitesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 sm:text-base\",\n                            children: \"Loading sites...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, undefined) : sites.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 sm:text-base\",\n                            children: \"No sites available. Please create a site first.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            value: selectedSiteId,\n                            onChange: (e)=>setSelectedSiteId(e.target.value),\n                            className: \"w-full p-2.5 sm:p-3 text-sm sm:text-base border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Select a site...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined),\n                                sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: site.id,\n                                        children: [\n                                            site.site_name,\n                                            \" (\",\n                                            site.expiry_status,\n                                            \")\"\n                                        ]\n                                    }, site.id, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 mb-6 bg-white rounded-lg shadow-sm sm:p-6 sm:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-3 text-lg font-semibold text-gray-800 sm:mb-4 sm:text-xl\",\n                            children: \"Search for Domain\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-4 text-sm text-gray-600 sm:text-base\",\n                            children: \"Enter a domain name to check availability across multiple extensions.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, undefined),\n                        !selectedSiteId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 mb-4 border rounded-md text-amber-700 bg-amber-50 border-amber-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs sm:text-sm\",\n                                children: \"⚠️ Please select a site above before searching for domains.\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-stretch gap-3 sm:flex-row sm:items-center sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domainName,\n                                    onChange: (e)=>setDomainName(e.target.value),\n                                    placeholder: \"Find your new domain (e.g., my-awesome-site)\",\n                                    className: \"flex-grow p-2.5 sm:p-3 text-sm sm:text-base transition border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500\",\n                                    onKeyDown: (e)=>e.key === \"Enter\" && handleSearch(e)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSearch,\n                                    disabled: loading || !domainName.trim() || !selectedSiteId,\n                                    className: \"px-4 sm:px-6 py-2.5 sm:py-3 text-sm sm:text-base font-bold text-white transition bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed whitespace-nowrap\",\n                                    title: !selectedSiteId ? \"Please select a site first\" : \"\",\n                                    children: loading ? \"Searching...\" : \"Search\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, undefined),\n                results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-white rounded-lg shadow-sm sm:p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col mb-4 space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800 sm:text-2xl\",\n                                    children: \"Results\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedSiteId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 sm:text-sm\",\n                                    children: [\n                                        \"Will be associated with: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium break-all\",\n                                            children: (_sites_find = sites.find((s)=>s.id === selectedSiteId)) === null || _sites_find === void 0 ? void 0 : _sites_find.site_name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 44\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 sm:space-y-4\",\n                            children: results.map((result)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border rounded-md sm:p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-base font-medium text-gray-700 break-all sm:text-lg\",\n                                                        children: result.Domain\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    result.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 sm:text-sm\",\n                                                        children: [\n                                                            \"AUD $\",\n                                                            result.Price.toFixed(2),\n                                                            \"/year\",\n                                                            result.IsPremiumName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 font-semibold text-orange-500\",\n                                                                children: \"Premium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 52\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4\",\n                                                children: result.Error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-red-500 sm:text-base\",\n                                                    children: result.Error\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 25\n                                                }, undefined) : result.Available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-bold text-green-600 sm:text-base\",\n                                                            children: \"Available!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleRegister(result.Domain),\n                                                            disabled: registering === result.Domain,\n                                                            className: \"px-3 sm:px-4 py-2 text-sm sm:text-base font-bold text-white transition bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed min-w-[100px] whitespace-nowrap\",\n                                                            children: registering === result.Domain ? \"Registering...\" : \"Register\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold text-red-500 sm:text-base\",\n                                                    children: \"Unavailable\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, result.Domain, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainPage, \"FQ537b+Mu7d0xERzjVZeod6fpk0=\", false, function() {\n    return [\n        _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DomainPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainPage);\nvar _c;\n$RefreshReg$(_c, \"DomainPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/domain/page.tsx\n"));

/***/ })

});