"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/domain/complete/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/domain/complete/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/domain/complete/page.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainCompletePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../components/providers/AuthProvider */ \"(app-pages-browser)/./src/components/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DomainCompletePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const sessionId = searchParams.get(\"session_id\");\n    const domain = searchParams.get(\"domain\");\n    const siteId = searchParams.get(\"siteId\");\n    const { user, loading: authLoading } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Verifying payment...\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const hasRun = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (hasRun.current) return;\n        hasRun.current = true;\n        const processDomainRegistration = async ()=>{\n            if (authLoading || !user) {\n                return;\n            }\n            if (!sessionId || !domain) {\n                setError(\"Missing required parameters (session ID or domain).\");\n                return;\n            }\n            console.log(\"Starting domain registration process:\", {\n                sessionId,\n                domain,\n                siteId,\n                userId: user.id\n            });\n            setStatus(\"Verifying payment...\");\n            try {\n                // 1. Verify payment\n                const verifyRes = await fetch(\"/api/verify-session\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        sessionId\n                    })\n                });\n                console.log(\"Payment verification response status:\", verifyRes.status);\n                const verifyData = await verifyRes.json();\n                console.log(\"Payment verification data:\", verifyData);\n                if (!verifyRes.ok || verifyData.error) {\n                    throw new Error(verifyData.error || \"Payment verification failed.\");\n                }\n                if (verifyData.paymentStatus !== \"paid\" && verifyData.status !== \"complete\") {\n                    throw new Error(\"Payment not completed. Status: \".concat(verifyData.paymentStatus, \", Session Status: \").concat(verifyData.status));\n                }\n                // Use siteId from payment metadata if available, otherwise use URL parameter\n                const finalSiteId = verifyData.siteId || siteId;\n                const finalUserId = verifyData.userId || user.id;\n                console.log(\"Payment verified, proceeding with registration:\", {\n                    domain,\n                    siteId: finalSiteId,\n                    userId: finalUserId\n                });\n                setStatus(\"Registering your domain...\");\n                // 2. Register domain\n                const registerRes = await fetch(\"/api/namecheap\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        domain,\n                        action: \"register\",\n                        siteId: finalSiteId,\n                        userId: finalUserId,\n                        stripeSessionId: sessionId\n                    })\n                });\n                console.log(\"Domain registration response status:\", registerRes.status);\n                const registerData = await registerRes.json();\n                console.log(\"Domain registration data:\", registerData);\n                if (!registerRes.ok || registerData.error) {\n                    throw new Error(registerData.error || \"Domain registration failed.\");\n                }\n                setStatus(\"Domain registered! Redirecting to dashboard...\");\n                setTimeout(()=>router.replace(\"/dashboard\"), 2000);\n            } catch (err) {\n                console.error(\"Domain registration error:\", err);\n                setError(err.message || \"An error occurred.\");\n                setStatus(\"\");\n            }\n        };\n        processDomainRegistration();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        sessionId,\n        domain,\n        siteId,\n        authLoading,\n        user\n    ]);\n    if (authLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Checking authentication...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n            lineNumber: 103,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-6 sm:p-8 rounded-lg shadow-md max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl sm:text-2xl font-bold mb-4 text-gray-800\",\n                    children: \"Domain Registration\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-sm sm:text-base text-gray-700\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 20\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600 font-semibold mb-4 text-sm sm:text-base break-words\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 11\n                }, this),\n                !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"animate-spin h-6 w-6 sm:h-8 sm:w-8 text-green-600\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"M4 12a8 8 0 018-8v8z\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"w-full sm:w-auto mt-4 px-4 py-2 text-sm sm:text-base bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                    onClick: ()=>router.replace(\"/dashboard/domain\"),\n                    children: \"Back to Domain Page\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainCompletePage, \"hpG9D36QVP5mWRKMYXteg2GEmP0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DomainCompletePage;\nvar _c;\n$RefreshReg$(_c, \"DomainCompletePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/domain/complete/page.tsx\n"));

/***/ })

});