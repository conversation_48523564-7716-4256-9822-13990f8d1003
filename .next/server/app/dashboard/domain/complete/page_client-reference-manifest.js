globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/domain/complete/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/AuthProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/AuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/domain/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/domain/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/domain/complete/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/domain/complete/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/src/app/styles/globals.css":{"id":"(app-pages-browser)/./src/app/styles/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/AuthProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/src/app/page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/layout.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/domain/page.tsx","name":"*","chunks":["app/dashboard/domain/page","static/chunks/app/dashboard/domain/page.js"],"async":false},"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/domain/complete/page.tsx","name":"*","chunks":["app/dashboard/domain/complete/page","static/chunks/app/dashboard/domain/complete/page.js"],"async":false}},"entryCSSFiles":{"/home/<USER>/Desktop/wp-ai-app/src/app/layout":["static/css/app/layout.css"],"/home/<USER>/Desktop/wp-ai-app/src/app/page":[],"/home/<USER>/Desktop/wp-ai-app/src/app/not-found":[],"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page":[],"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout":[],"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/page":[],"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page":[]}}