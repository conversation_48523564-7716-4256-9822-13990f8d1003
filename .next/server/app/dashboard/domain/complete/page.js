/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/domain/complete/page";
exports.ids = ["app/dashboard/domain/complete/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&page=%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&appPaths=%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fdomain%2Fcomplete%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&page=%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&appPaths=%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fdomain%2Fcomplete%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'domain',\n        {\n        children: [\n        'complete',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/domain/complete/page.tsx */ \"(rsc)/./src/app/dashboard/domain/complete/page.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/domain/complete/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/domain/complete/page\",\n        pathname: \"/dashboard/domain/complete\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZkYXNoYm9hcmQlMkZkb21haW4lMkZjb21wbGV0ZSUyRnBhZ2UmcGFnZT0lMkZkYXNoYm9hcmQlMkZkb21haW4lMkZjb21wbGV0ZSUyRnBhZ2UmYXBwUGF0aHM9JTJGZGFzaGJvYXJkJTJGZG9tYWluJTJGY29tcGxldGUlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZGFzaGJvYXJkJTJGZG9tYWluJTJGY29tcGxldGUlMkZwYWdlLnRzeCZhcHBEaXI9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLG9NQUE0RztBQUNuSTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLHdLQUE4RjtBQUN2SDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsb0pBQW9GO0FBQzdHLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz82ODNjIl0sInNvdXJjZXNDb250ZW50IjpbIlwiVFVSQk9QQUNLIHsgdHJhbnNpdGlvbjogbmV4dC1zc3IgfVwiO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2Rhc2hib2FyZCcsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2RvbWFpbicsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2NvbXBsZXRlJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2Rhc2hib2FyZC9kb21haW4vY29tcGxldGUvcGFnZS50c3hcIiksIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2Rhc2hib2FyZC9kb21haW4vY29tcGxldGUvcGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4XCIpLCBcIi9ob21lL2RlbGwvRGVza3RvcC93cC1haS1hcHAvc3JjL2FwcC9kYXNoYm9hcmQvbGF5b3V0LnRzeFwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9kZWxsL0Rlc2t0b3Avd3AtYWktYXBwL3NyYy9hcHAvbGF5b3V0LnRzeFwiKSwgXCIvaG9tZS9kZWxsL0Rlc2t0b3Avd3AtYWktYXBwL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2Rhc2hib2FyZC9kb21haW4vY29tcGxldGUvcGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9kYXNoYm9hcmQvZG9tYWluL2NvbXBsZXRlL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvZGFzaGJvYXJkL2RvbWFpbi9jb21wbGV0ZS9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9kYXNoYm9hcmQvZG9tYWluL2NvbXBsZXRlXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&page=%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&appPaths=%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fdomain%2Fcomplete%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fdomain%2Fcomplete%2Fpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fdomain%2Fcomplete%2Fpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/domain/complete/page.tsx */ \"(ssr)/./src/app/dashboard/domain/complete/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGZG9tYWluJTJGY29tcGxldGUlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz83NTZlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2Rhc2hib2FyZC9kb21haW4vY29tcGxldGUvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fdomain%2Fcomplete%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz83Y2EzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(ssr)/./src/components/providers/AuthProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGc3R5bGVzJTJGZ2xvYmFscy5jc3MmbW9kdWxlcz0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGQXV0aFByb3ZpZGVyLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz8yNzE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/domain/complete/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/domain/complete/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DomainCompletePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../components/providers/AuthProvider */ \"(ssr)/./src/components/providers/AuthProvider.tsx\");\n/* harmony import */ var _components_domain_DomainSiteMappingStep__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../components/domain/DomainSiteMappingStep */ \"(ssr)/./src/components/domain/DomainSiteMappingStep.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DomainCompletePage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const sessionId = searchParams.get(\"session_id\");\n    const domain = searchParams.get(\"domain\");\n    const siteId = searchParams.get(\"siteId\");\n    const { user, session, loading: authLoading } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Processing your domain registration...\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"registration\");\n    const [registeredDomain, setRegisteredDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const hasRun = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const processDomainRegistration = async ()=>{\n            if (hasRun.current || authLoading || !user) {\n                console.log(\"Conditions not met for registration:\", {\n                    hasRun: hasRun.current,\n                    authLoading,\n                    user: !!user\n                });\n                return;\n            }\n            hasRun.current = true;\n            if (!sessionId || !domain) {\n                setError(\"Missing required parameters (session ID or domain).\");\n                return;\n            }\n            console.log(\"Starting domain registration process:\", {\n                sessionId,\n                domain,\n                siteId,\n                userId: user.id\n            });\n            // Skip payment verification - proceed directly to domain registration\n            // Since Stripe redirected to success URL, we trust the payment was successful\n            try {\n                console.log(\"Proceeding with domain registration...\");\n                setStatus(\"Registering your domain...\");\n                // Register domain directly with Namecheap\n                const registerRes = await fetch(\"/api/namecheap\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        domain,\n                        action: \"register\",\n                        siteId: siteId || \"pending\",\n                        userId: user.id,\n                        stripeSessionId: sessionId\n                    })\n                });\n                console.log(\"Domain registration response status:\", registerRes.status);\n                const registerData = await registerRes.json();\n                console.log(\"Domain registration data:\", registerData);\n                if (!registerRes.ok || registerData.error) {\n                    throw new Error(registerData.error || \"Domain registration failed.\");\n                }\n                // Domain registration successful - move to site mapping step\n                setRegisteredDomain(domain);\n                setCurrentStep(\"mapping\");\n                setStatus(\"Domain registered successfully! Now let's connect it to your site.\");\n            } catch (err) {\n                console.error(\"Domain registration error:\", err);\n                setError(err.message || \"An error occurred.\");\n                setStatus(\"\");\n            }\n        };\n        processDomainRegistration();\n    }, [\n        sessionId,\n        domain,\n        siteId,\n        authLoading,\n        user,\n        router\n    ]);\n    // Handle site mapping completion\n    const handleSiteMappingComplete = async (selectedSiteId)=>{\n        if (!registeredDomain || !user || !session) {\n            throw new Error(\"Missing domain, user, or session information\");\n        }\n        setStatus(\"Configuring DNS and mapping domain to site...\");\n        try {\n            // Get the user's session token for authentication\n            const token = session.access_token;\n            if (!token) {\n                throw new Error(\"Authentication token not available\");\n            }\n            const response = await fetch(\"/api/domain-mapping\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    domainName: registeredDomain,\n                    siteId: selectedSiteId\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || data.error) {\n                throw new Error(data.error || \"Failed to map domain to site\");\n            }\n            setCurrentStep(\"complete\");\n            setStatus(\"Domain setup complete! Redirecting to dashboard...\");\n            setTimeout(()=>router.replace(\"/dashboard\"), 2000);\n        } catch (error) {\n            console.error(\"Site mapping error:\", error);\n            throw error; // Re-throw to be handled by the DomainSiteMappingStep component\n        }\n    };\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white p-6 sm:p-8 rounded-lg shadow-md max-w-md w-full text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl sm:text-2xl font-bold mb-4 text-gray-800\",\n                        children: \"Verifying Authentication\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Please wait while we confirm your session...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this);\n    }\n    // Show site mapping step if domain registration is complete\n    if (currentStep === \"mapping\" && registeredDomain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domain_DomainSiteMappingStep__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    domainName: registeredDomain,\n                    onComplete: handleSiteMappingComplete\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this);\n    }\n    // Show registration progress or completion\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white p-6 sm:p-8 rounded-lg shadow-md max-w-md w-full text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl sm:text-2xl font-bold mb-4 text-gray-800\",\n                    children: currentStep === \"complete\" ? \"Domain Setup Complete!\" : \"Completing Domain Registration\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-sm sm:text-base text-gray-700\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 20\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600 font-semibold mb-4 text-sm sm:text-base break-words\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, this),\n                !error && currentStep !== \"complete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"animate-spin h-6 w-6 sm:h-8 sm:w-8 text-green-600\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                className: \"opacity-25\",\n                                cx: \"12\",\n                                cy: \"12\",\n                                r: \"10\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                className: \"opacity-75\",\n                                fill: \"currentColor\",\n                                d: \"M4 12a8 8 0 018-8v8z\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 11\n                }, this),\n                currentStep === \"complete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-12 w-12 text-green-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: \"2\",\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"w-full sm:w-auto mt-4 px-4 py-2 text-sm sm:text-base bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\",\n                    onClick: ()=>router.replace(\"/dashboard/domain\"),\n                    children: \"Back to Domain Page\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/domain/complete/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/providers/AuthProvider */ \"(ssr)/./src/components/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { profile, loading, signOut } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [signingOut, setSigningOut] = react__WEBPACK_IMPORTED_MODULE_2___default().useState(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = react__WEBPACK_IMPORTED_MODULE_2___default().useState(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-[#eaf3e1]\",\n        children: [\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setIsMobileMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"hidden lg:flex w-64 bg-[#3d5c3a] text-white flex-col justify-between py-6 px-0 fixed inset-y-0 left-0 z-30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center px-8 mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#eaf3e1] rounded-lg w-10 h-10 flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-[#3d5c3a]\",\n                                        children: \"W\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold tracking-tight\",\n                                    children: \"AI builder\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Websites\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/create\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/domain\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/account\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/billing\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Billing\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/settings\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: `lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-[#3d5c3a] text-white transform transition-transform duration-300 ease-in-out ${isMobileMenuOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-8 mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#eaf3e1] rounded-lg w-10 h-10 flex items-center justify-center mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-[#3d5c3a]\",\n                                                children: \"W\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold tracking-tight\",\n                                            children: \"AI builder\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: \"p-2 rounded-md hover:bg-[#4e6e4a] transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Websites\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/create\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/domain\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/account\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/billing\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Billing\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/settings\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col flex-1 min-h-screen overflow-x-auto lg:ml-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 z-10 bg-white border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4 sm:px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(true),\n                                    className: \"p-2 text-gray-600 rounded-md hover:bg-gray-100 lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:block\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"hidden p-2 rounded-full hover:bg-gray-100 sm:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600 sm:w-6 sm:h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"hidden p-2 rounded-full hover:bg-gray-100 sm:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600 sm:w-6 sm:h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"hidden p-2 rounded-full hover:bg-gray-100 sm:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600 sm:w-6 sm:h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden ml-2 text-sm font-medium text-gray-700 md:block lg:text-base\",\n                                            children: loading ? \"Loading...\" : profile?.email || \"Not signed in\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-2 py-1 text-sm text-white bg-red-500 rounded sm:px-4 sm:py-2 hover:bg-red-600 disabled:bg-gray-300 disabled:text-gray-500 sm:text-base\",\n                                            onClick: async ()=>{\n                                                setSigningOut(true);\n                                                await signOut();\n                                                setSigningOut(false);\n                                                router.replace(\"/login\");\n                                            },\n                                            disabled: signingOut,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: signingOut ? \"Signing out...\" : \"Sign Out\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 overflow-y-auto sm:p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/domain/DomainSiteMappingStep.tsx":
/*!*********************************************************!*\
  !*** ./src/components/domain/DomainSiteMappingStep.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,ExternalLink,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,ExternalLink,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,ExternalLink,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,ExternalLink,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,ExternalLink,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst DomainSiteMappingStep = ({ domainName, siteId: preSelectedSiteId, onComplete, onBack })=>{\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSiteId, setSelectedSiteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(preSelectedSiteId || \"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const { data, error } = await supabase.from(\"user-websites\").select(\"id, site_name, expiry_status, expiry_time\").order(\"created_at\", {\n                    ascending: false\n                });\n                if (error) {\n                    throw error;\n                }\n                setSites(data);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n                setError(\"Failed to load your sites. Please try again.\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSites();\n    }, [\n        supabase\n    ]);\n    const handleSiteSelect = (siteId)=>{\n        setSelectedSiteId(siteId);\n    };\n    const handleComplete = async ()=>{\n        if (!selectedSiteId) {\n            setError(\"Please select a site to map your domain to.\");\n            return;\n        }\n        setProcessing(true);\n        setError(null);\n        try {\n            // Call the completion handler\n            await onComplete(selectedSiteId);\n        } catch (err) {\n            console.error(\"Error completing domain mapping:\", err);\n            setError(err.message || \"Failed to complete domain mapping. Please try again.\");\n            setProcessing(false);\n        }\n    };\n    const getSiteStatusBadge = (site)=>{\n        if (site.expiry_status === \"Permanent\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full font-medium\",\n                children: \"Permanent\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"px-2 py-1 bg-amber-100 text-amber-700 text-xs rounded-full font-medium\",\n            children: \"Temporary\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-8 h-8 text-gray-400 animate-spin mr-3\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading your sites...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-2\",\n                        children: \"Map Your Domain\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Choose which site you'd like to connect to \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: domainName\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 54\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-6 h-6 text-green-600 mr-3\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium text-green-800\",\n                                    children: \"Domain Successfully Registered!\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-green-600\",\n                                    children: [\n                                        domainName,\n                                        \" is now yours. Let's connect it to one of your sites.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-5 h-5 text-red-500 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-700 font-medium\",\n                                children: \"Error\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm mt-1\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, undefined),\n            sites.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-6 max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400 mx-auto mb-3\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-600 mb-2\",\n                            children: \"No Sites Available\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 text-sm mb-4\",\n                            children: \"You need to create a site first before you can map a domain to it.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/dashboard/create\",\n                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Create New Site\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-800\",\n                        children: \"Select a Site\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-3\",\n                        children: sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `border rounded-lg p-4 cursor-pointer transition-all ${selectedSiteId === site.id ? \"border-green-500 bg-green-50 ring-2 ring-green-200\" : \"border-gray-200 hover:border-green-300 hover:bg-green-50\"}`,\n                                onClick: ()=>handleSiteSelect(site.id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-4 h-4 rounded-full border-2 flex items-center justify-center ${selectedSiteId === site.id ? \"border-green-500 bg-green-500\" : \"border-gray-300\"}`,\n                                                    children: selectedSiteId === site.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium text-gray-800 truncate\",\n                                                            children: site.site_name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2 mt-1\",\n                                                            children: [\n                                                                getSiteStatusBadge(site),\n                                                                site.expiry_time && site.expiry_status === \"Temporary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Expires: \",\n                                                                        new Date(site.expiry_time).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        selectedSiteId === site.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-6 h-6 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, site.id, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                lineNumber: 159,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-blue-800 mb-2\",\n                        children: \"What happens next?\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-sm text-blue-700 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Your domain will be configured to point to the selected site\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• DNS settings will be automatically updated\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• Your site will be accessible via the new domain within a few minutes\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• You can change the mapping later from your domain management page\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-3 pt-4\",\n                children: [\n                    onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        disabled: processing,\n                        className: \"flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleComplete,\n                        disabled: processing || !selectedSiteId || sites.length === 0,\n                        className: \"flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                        children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Configuring...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_ExternalLink_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Complete Domain Setup\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/domain/DomainSiteMappingStep.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomainSiteMappingStep);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/domain/DomainSiteMappingStep.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch profile from public.profiles\n    const fetchProfile = async (email)=>{\n        const { data, error } = await supabase.from(\"profiles\").select(\"first_name, last_name, email\").eq(\"email\", email).single();\n        if (error) {\n            setProfile(null);\n            setError(error.message);\n        } else {\n            setProfile(data);\n        }\n    };\n    // Update profile in public.profiles\n    const updateProfile = async (updates)=>{\n        if (!profile) return;\n        setLoading(true);\n        const { error } = await supabase.from(\"profiles\").update(updates).eq(\"email\", profile.email);\n        if (error) {\n            setError(error.message);\n        } else {\n            setProfile({\n                ...profile,\n                ...updates\n            });\n        }\n        setLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getSession = async ()=>{\n            setLoading(true);\n            const { data, error } = await supabase.auth.getSession();\n            if (error) {\n                setError(error.message);\n                setSession(null);\n                setUser(null);\n                setProfile(null);\n            } else {\n                setSession(data.session);\n                setUser(data.session?.user ?? null);\n                if (data.session?.user?.email) {\n                    await fetchProfile(data.session.user.email);\n                } else {\n                    setProfile(null);\n                }\n            }\n            setLoading(false);\n        };\n        getSession();\n        const { data: listener } = supabase.auth.onAuthStateChange(async (_event, session)=>{\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user?.email) {\n                await fetchProfile(session.user.email);\n            } else {\n                setProfile(null);\n            }\n        });\n        return ()=>{\n            listener.subscription.unsubscribe();\n        };\n    }, []);\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        setError(null);\n        const { error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) setError(error.message);\n        setLoading(false);\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        setError(null);\n        const { error } = await supabase.auth.signOut();\n        if (error) setError(error.message);\n        setLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            profile,\n            setProfile,\n            updateProfile,\n            loading,\n            error,\n            signIn,\n            signOut\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd8cab21ee81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcz8yNTZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2Q4Y2FiMjFlZTgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/domain/complete/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/dashboard/domain/complete/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/domain/complete/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./styles/globals.css */ \"(rsc)/./src/app/styles/globals.css\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/providers/AuthProvider */ \"(rsc)/./src/components/providers/AuthProvider.tsx\");\n\n\n\nconst metadata = {\n    title: \"WordPress AI Builder\",\n    description: \"Automated WordPress site generation with AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNzQztBQUU3RCxNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQWlDO0lBQzVFLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ1IsNEVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnLi4vY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1dvcmRQcmVzcyBBSSBCdWlsZGVyJyxcbiAgZGVzY3JpcHRpb246ICdBdXRvbWF0ZWQgV29yZFByZXNzIHNpdGUgZ2VuZXJhdGlvbiB3aXRoIEFJJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/set-cookie-parser","vendor-chunks/jose"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&page=%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&appPaths=%2Fdashboard%2Fdomain%2Fcomplete%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fdomain%2Fcomplete%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();