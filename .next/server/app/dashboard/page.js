/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGYXBwLXJvdXRlci5qcyZtb2R1bGVzPSUyRmhvbWUlMkZkZWxsJTJGRGVza3RvcCUyRndwLWFpLWFwcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRmhvbWUlMkZkZWxsJTJGRGVza3RvcCUyRndwLWFpLWFwcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm5vdC1mb3VuZC1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRmhvbWUlMkZkZWxsJTJGRGVza3RvcCUyRndwLWFpLWFwcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMmbW9kdWxlcz0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQXdIO0FBQ3hILDBPQUE0SDtBQUM1SCx3T0FBMkg7QUFDM0gsa1BBQWdJO0FBQ2hJLHNRQUEwSTtBQUMxSSIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvP2RmNzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9kZWxsL0Rlc2t0b3Avd3AtYWktYXBwL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9kZWxsL0Rlc2t0b3Avd3AtYWktYXBwL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2RlbGwvRGVza3RvcC93cC1haS1hcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGbGF5b3V0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz83Y2EzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8/MjI4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL2RlbGwvRGVza3RvcC93cC1haS1hcHAvc3JjL2FwcC9kYXNoYm9hcmQvcGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(ssr)/./src/components/providers/AuthProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGc3R5bGVzJTJGZ2xvYmFscy5jc3MmbW9kdWxlcz0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGQXV0aFByb3ZpZGVyLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz8yNzE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/AskDomainModal.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/AskDomainModal.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst AskDomainModal = ({ isOpen, onYes, onNo })=>{\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-full max-w-md p-6 bg-white rounded-lg shadow-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"mb-2 text-xl font-semibold text-gray-800 text-center\",\n                    children: \"Thank you for your purchase!\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-4 text-center text-gray-700\",\n                    children: \"Do you already have a custom domain you’d like to use?\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onYes,\n                            className: \"px-4 py-2 text-white bg-green-600 rounded-md hover:bg-green-700\",\n                            children: \"Yes\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onNo,\n                            className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\",\n                            children: \"No\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/AskDomainModal.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AskDomainModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/AskDomainModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/MapDomainModal.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/MapDomainModal.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst MapDomainModal = ({ isOpen, onClose, siteName })=>{\n    const [domainName, setDomainName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [domainType, setDomainType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Primary\");\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-gray-600 bg-opacity-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md p-6 bg-white rounded-lg shadow-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold\",\n                            children: \"Map Domain Name\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"domainName\",\n                            className: \"block text-sm font-medium text-gray-700\",\n                            children: \"Enter Domain Name\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            id: \"domainName\",\n                            className: \"block w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                            placeholder: \"example.com\",\n                            value: domainName,\n                            onChange: (e)=>setDomainName(e.target.value)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"inline-flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"radio\",\n                                        className: \"form-radio text-blue-600\",\n                                        name: \"domainType\",\n                                        value: \"Primary\",\n                                        checked: domainType === \"Primary\",\n                                        onChange: ()=>setDomainType(\"Primary\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"Primary\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"inline-flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"radio\",\n                                        className: \"form-radio text-blue-600\",\n                                        name: \"domainType\",\n                                        value: \"Alias\",\n                                        checked: domainType === \"Alias\",\n                                        onChange: ()=>setDomainType(\"Alias\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: \"Alias\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Point 'CNAME' records to\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-3 mt-1 bg-gray-50 border border-gray-300 rounded-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex-1 text-sm text-gray-800 break-all\",\n                                    children: siteName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"ml-2 text-gray-500 hover:text-gray-700\",\n                                    onClick: ()=>navigator.clipboard.writeText(siteName),\n                                    title: \"Copy to clipboard\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M17 16l-4 4-4-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-4 py-2 text-white bg-green-500 rounded-md hover:bg-green-600\",\n                            onClick: ()=>{\n                                // Handle map domain logic here\n                                console.log(\"Mapping domain:\", domainName, \"as\", domainType);\n                                onClose();\n                            },\n                            children: \"Map Domain\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/MapDomainModal.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MapDomainModal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/MapDomainModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Globe,HelpCircle,Link,Menu,PlusCircle,Settings,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/providers/AuthProvider */ \"(ssr)/./src/components/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardLayout({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { profile, loading, signOut } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [signingOut, setSigningOut] = react__WEBPACK_IMPORTED_MODULE_2___default().useState(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = react__WEBPACK_IMPORTED_MODULE_2___default().useState(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-[#eaf3e1]\",\n        children: [\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setIsMobileMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"hidden lg:flex w-64 bg-[#3d5c3a] text-white flex-col justify-between py-6 px-0 fixed inset-y-0 left-0 z-30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center px-8 mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[#eaf3e1] rounded-lg w-10 h-10 flex items-center justify-center mr-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-[#3d5c3a]\",\n                                        children: \"W\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold tracking-tight\",\n                                    children: \"AI builder\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Websites\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/create\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/domain\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/account\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/billing\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Billing\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/settings\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: `lg:hidden fixed inset-y-0 left-0 z-50 w-64 bg-[#3d5c3a] text-white transform transition-transform duration-300 ease-in-out ${isMobileMenuOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full py-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-8 mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#eaf3e1] rounded-lg w-10 h-10 flex items-center justify-center mr-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-[#3d5c3a]\",\n                                                children: \"W\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold tracking-tight\",\n                                            children: \"AI builder\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    className: \"p-2 rounded-md hover:bg-[#4e6e4a] transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Websites\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/create\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Create\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/domain\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/account\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/billing\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Billing\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: \"/dashboard/settings\",\n                                            className: `flex items-center px-8 py-3 rounded-l-full transition-colors ${ false ? 0 : \"hover:bg-[#4e6e4a] hover:text-white\"}`,\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-4\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col flex-1 min-h-screen overflow-x-auto lg:ml-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 z-10 bg-white border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16 px-4 sm:px-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileMenuOpen(true),\n                                    className: \"p-2 text-gray-600 rounded-md hover:bg-gray-100 lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:block\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 sm:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"hidden p-2 rounded-full hover:bg-gray-100 sm:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600 sm:w-6 sm:h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"hidden p-2 rounded-full hover:bg-gray-100 sm:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600 sm:w-6 sm:h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"hidden p-2 rounded-full hover:bg-gray-100 sm:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Globe_HelpCircle_Link_Menu_PlusCircle_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-gray-600 sm:w-6 sm:h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden ml-2 text-sm font-medium text-gray-700 md:block lg:text-base\",\n                                            children: loading ? \"Loading...\" : profile?.email || \"Not signed in\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-2 py-1 text-sm text-white bg-red-500 rounded sm:px-4 sm:py-2 hover:bg-red-600 disabled:bg-gray-300 disabled:text-gray-500 sm:text-base\",\n                                            onClick: async ()=>{\n                                                setSigningOut(true);\n                                                await signOut();\n                                                setSigningOut(false);\n                                                router.replace(\"/login\");\n                                            },\n                                            disabled: signingOut,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: signingOut ? \"Signing out...\" : \"Sign Out\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 overflow-y-auto sm:p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkI7QUFDSDtBQUNnRztBQUM5RTtBQUNzQjtBQUVuRCxTQUFTYyxnQkFBZ0IsRUFBRUMsUUFBUSxFQUFpQztJQUNqRixNQUFNQyxTQUFTSiwwREFBU0E7SUFDeEIsTUFBTSxFQUFFSyxPQUFPLEVBQUVDLE9BQU8sRUFBRUMsT0FBTyxFQUFFLEdBQUdOLDJFQUFPQTtJQUM3QyxNQUFNLENBQUNPLFlBQVlDLGNBQWMsR0FBR3BCLHFEQUFjLENBQUM7SUFDbkQsTUFBTSxDQUFDc0Isa0JBQWtCQyxvQkFBb0IsR0FBR3ZCLHFEQUFjLENBQUM7SUFHL0QscUJBQ0UsOERBQUN3QjtRQUFJQyxXQUFVOztZQUVaSCxrQ0FDQyw4REFBQ0U7Z0JBQ0NDLFdBQVU7Z0JBQ1ZDLFNBQVMsSUFBTUgsb0JBQW9COzs7Ozs7MEJBS3ZDLDhEQUFDSTtnQkFBTUYsV0FBVTswQkFDZiw0RUFBQ0Q7O3NDQUNDLDhEQUFDQTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRzt3Q0FBS0gsV0FBVTtrREFBb0M7Ozs7Ozs7Ozs7OzhDQUV0RCw4REFBQ0c7b0NBQUtILFdBQVU7OENBQW9DOzs7Ozs7Ozs7Ozs7c0NBRXRELDhEQUFDSTtzQ0FDQyw0RUFBQ0M7Z0NBQUdMLFdBQVU7O2tEQUNaLDhEQUFDTTtrREFDQyw0RUFBQ2hDLGlEQUFJQTs0Q0FBQ2lDLE1BQUs7NENBQWFQLFdBQVcsQ0FBQyw2REFBNkQsRUFBRSxNQUE4RCxHQUFlLElBQWlELHNDQUFzQyxDQUFDOzs4REFDdFEsOERBQUNyQixpSkFBS0E7b0RBQUNxQixXQUFVOzs7Ozs7OERBQ2pCLDhEQUFDRztvREFBS0gsV0FBVTs4REFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzNCLDhEQUFDTTtrREFDQyw0RUFBQ2hDLGlEQUFJQTs0Q0FBQ2lDLE1BQUs7NENBQW9CUCxXQUFXLENBQUMsNkRBQTZELEVBQUUsTUFBOEQsR0FBc0IsSUFBaUQsc0NBQXNDLENBQUM7OzhEQUNwUiw4REFBQ3BCLGlKQUFVQTtvREFBQ29CLFdBQVU7Ozs7Ozs4REFDdEIsOERBQUNHO29EQUFLSCxXQUFVOzhEQUFPOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHM0IsOERBQUNNO2tEQUNDLDRFQUFDaEMsaURBQUlBOzRDQUFDaUMsTUFBSzs0Q0FBb0JQLFdBQVcsQ0FBQyw2REFBNkQsRUFBRSxNQUE4RCxHQUFzQixJQUFpRCxzQ0FBc0MsQ0FBQzs7OERBQ3BSLDhEQUFDakIsaUpBQVFBO29EQUFDaUIsV0FBVTs7Ozs7OzhEQUNwQiw4REFBQ0c7b0RBQUtILFdBQVU7OERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUczQiw4REFBQ007a0RBQ0MsNEVBQUNoQyxpREFBSUE7NENBQUNpQyxNQUFLOzRDQUFxQlAsV0FBVyxDQUFDLDZEQUE2RCxFQUFFLE1BQThELEdBQXVCLElBQWlELHNDQUFzQyxDQUFDOzs4REFDdFIsOERBQUN0QixpSkFBSUE7b0RBQUNzQixXQUFVOzs7Ozs7OERBQ2hCLDhEQUFDRztvREFBS0gsV0FBVTs4REFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzNCLDhEQUFDTTtrREFDQyw0RUFBQ2hDLGlEQUFJQTs0Q0FBQ2lDLE1BQUs7NENBQXFCUCxXQUFXLENBQUMsNkRBQTZELEVBQUUsTUFBOEQsR0FBdUIsSUFBaUQsc0NBQXNDLENBQUM7OzhEQUN0Uiw4REFBQ2xCLGlKQUFVQTtvREFBQ2tCLFdBQVU7Ozs7Ozs4REFDdEIsOERBQUNHO29EQUFLSCxXQUFVOzhEQUFPOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHM0IsOERBQUNNO2tEQUNDLDRFQUFDaEMsaURBQUlBOzRDQUFDaUMsTUFBSzs0Q0FBc0JQLFdBQVcsQ0FBQyw2REFBNkQsRUFBRSxNQUE4RCxHQUF3QixJQUFpRCxzQ0FBc0MsQ0FBQzs7OERBQ3hSLDhEQUFDbkIsa0pBQVFBO29EQUFDbUIsV0FBVTs7Ozs7OzhEQUNwQiw4REFBQ0c7b0RBQUtILFdBQVU7OERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTbkMsOERBQUNFO2dCQUFNRixXQUFXLENBQUMsMkhBQTJILEVBQUVILG1CQUFtQixrQkFBa0Isb0JBQW9CLENBQUM7MEJBQ3hNLDRFQUFDRTtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0c7Z0RBQUtILFdBQVU7MERBQW9DOzs7Ozs7Ozs7OztzREFFdEQsOERBQUNHOzRDQUFLSCxXQUFVO3NEQUFvQzs7Ozs7Ozs7Ozs7OzhDQUV0RCw4REFBQ1c7b0NBQ0NWLFNBQVMsSUFBTUgsb0JBQW9CO29DQUNuQ0UsV0FBVTs4Q0FFViw0RUFBQ2Ysa0pBQUNBO3dDQUFDZSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHakIsOERBQUNJOzRCQUFJSixXQUFVO3NDQUNiLDRFQUFDSztnQ0FBR0wsV0FBVTs7a0RBQ1osOERBQUNNO2tEQUNDLDRFQUFDaEMsaURBQUlBOzRDQUNIaUMsTUFBSzs0Q0FDTFAsV0FBVyxDQUFDLDZEQUE2RCxFQUFFLE1BQThELEdBQWUsSUFBaUQsc0NBQXNDLENBQUM7NENBQ2hQQyxTQUFTLElBQU1ILG9CQUFvQjs7OERBRW5DLDhEQUFDbkIsaUpBQUtBO29EQUFDcUIsV0FBVTs7Ozs7OzhEQUNqQiw4REFBQ0c7b0RBQUtILFdBQVU7OERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUczQiw4REFBQ007a0RBQ0MsNEVBQUNoQyxpREFBSUE7NENBQ0hpQyxNQUFLOzRDQUNMUCxXQUFXLENBQUMsNkRBQTZELEVBQUUsTUFBOEQsR0FBc0IsSUFBaUQsc0NBQXNDLENBQUM7NENBQ3ZQQyxTQUFTLElBQU1ILG9CQUFvQjs7OERBRW5DLDhEQUFDbEIsaUpBQVVBO29EQUFDb0IsV0FBVTs7Ozs7OzhEQUN0Qiw4REFBQ0c7b0RBQUtILFdBQVU7OERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUczQiw4REFBQ007a0RBQ0MsNEVBQUNoQyxpREFBSUE7NENBQ0hpQyxNQUFLOzRDQUNMUCxXQUFXLENBQUMsNkRBQTZELEVBQUUsTUFBOEQsR0FBc0IsSUFBaUQsc0NBQXNDLENBQUM7NENBQ3ZQQyxTQUFTLElBQU1ILG9CQUFvQjs7OERBRW5DLDhEQUFDZixpSkFBUUE7b0RBQUNpQixXQUFVOzs7Ozs7OERBQ3BCLDhEQUFDRztvREFBS0gsV0FBVTs4REFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzNCLDhEQUFDTTtrREFDQyw0RUFBQ2hDLGlEQUFJQTs0Q0FDSGlDLE1BQUs7NENBQ0xQLFdBQVcsQ0FBQyw2REFBNkQsRUFBRSxNQUE4RCxHQUF1QixJQUFpRCxzQ0FBc0MsQ0FBQzs0Q0FDeFBDLFNBQVMsSUFBTUgsb0JBQW9COzs4REFFbkMsOERBQUNwQixpSkFBSUE7b0RBQUNzQixXQUFVOzs7Ozs7OERBQ2hCLDhEQUFDRztvREFBS0gsV0FBVTs4REFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzNCLDhEQUFDTTtrREFDQyw0RUFBQ2hDLGlEQUFJQTs0Q0FDSGlDLE1BQUs7NENBQ0xQLFdBQVcsQ0FBQyw2REFBNkQsRUFBRSxNQUE4RCxHQUF1QixJQUFpRCxzQ0FBc0MsQ0FBQzs0Q0FDeFBDLFNBQVMsSUFBTUgsb0JBQW9COzs4REFFbkMsOERBQUNoQixpSkFBVUE7b0RBQUNrQixXQUFVOzs7Ozs7OERBQ3RCLDhEQUFDRztvREFBS0gsV0FBVTs4REFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBRzNCLDhEQUFDTTtrREFDQyw0RUFBQ2hDLGlEQUFJQTs0Q0FDSGlDLE1BQUs7NENBQ0xQLFdBQVcsQ0FBQyw2REFBNkQsRUFBRSxNQUE4RCxHQUF3QixJQUFpRCxzQ0FBc0MsQ0FBQzs0Q0FDelBDLFNBQVMsSUFBTUgsb0JBQW9COzs4REFFbkMsOERBQUNqQixrSkFBUUE7b0RBQUNtQixXQUFVOzs7Ozs7OERBQ3BCLDhEQUFDRztvREFBS0gsV0FBVTs4REFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNuQyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDWTt3QkFBT1osV0FBVTtrQ0FDaEIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ1c7b0NBQ0NWLFNBQVMsSUFBTUgsb0JBQW9CO29DQUNuQ0UsV0FBVTs4Q0FFViw0RUFBQ2hCLGtKQUFJQTt3Q0FBQ2dCLFdBQVU7Ozs7Ozs7Ozs7OzhDQUlsQiw4REFBQ0Q7b0NBQUlDLFdBQVU7Ozs7Ozs4Q0FHZiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDVzs0Q0FBT1gsV0FBVTtzREFDaEIsNEVBQUN4QixrSkFBSUE7Z0RBQUN3QixXQUFVOzs7Ozs7Ozs7OztzREFFbEIsOERBQUNXOzRDQUFPWCxXQUFVO3NEQUNoQiw0RUFBQ3ZCLGtKQUFVQTtnREFBQ3VCLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV4Qiw4REFBQ1c7NENBQU9YLFdBQVU7c0RBQ2hCLDRFQUFDdEIsaUpBQUlBO2dEQUFDc0IsV0FBVTs7Ozs7Ozs7Ozs7c0RBR2xCLDhEQUFDRzs0Q0FBS0gsV0FBVTtzREFDYlIsVUFBVSxlQUFlRCxTQUFTc0IsU0FBUzs7Ozs7O3NEQUc5Qyw4REFBQ0Y7NENBQ0NYLFdBQVU7NENBQ1ZDLFNBQVM7Z0RBQ1BOLGNBQWM7Z0RBQ2QsTUFBTUY7Z0RBQ05FLGNBQWM7Z0RBQ2RMLE9BQU93QixPQUFPLENBQUM7NENBQ2pCOzRDQUNBQyxVQUFVckI7c0RBRVYsNEVBQUNTOzBEQUNFVCxhQUFhLG1CQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRM0MsOERBQUNzQjt3QkFBS2hCLFdBQVU7a0NBQ2JYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4PzcyM2MiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQmVsbCwgSGVscENpcmNsZSwgVXNlciwgR2xvYmUsIFBsdXNDaXJjbGUsIFNldHRpbmdzLCBDcmVkaXRDYXJkLCBMaW5rIGFzIExpbmtJY29uLCBNZW51LCBYIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkTGF5b3V0KHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHsgcHJvZmlsZSwgbG9hZGluZywgc2lnbk91dCB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCBbc2lnbmluZ091dCwgc2V0U2lnbmluZ091dF0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc01vYmlsZU1lbnVPcGVuLCBzZXRJc01vYmlsZU1lbnVPcGVuXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcblxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIGJnLVsjZWFmM2UxXVwiPlxuICAgICAgey8qIE1vYmlsZSBtZW51IG92ZXJsYXkgKi99XG4gICAgICB7aXNNb2JpbGVNZW51T3BlbiAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotNDAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBsZzpoaWRkZW5cIlxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qIFNpZGViYXIgLSBEZXNrdG9wICovfVxuICAgICAgPGFzaWRlIGNsYXNzTmFtZT1cImhpZGRlbiBsZzpmbGV4IHctNjQgYmctWyMzZDVjM2FdIHRleHQtd2hpdGUgZmxleC1jb2wganVzdGlmeS1iZXR3ZWVuIHB5LTYgcHgtMCBmaXhlZCBpbnNldC15LTAgbGVmdC0wIHotMzBcIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHB4LTggbWItMTBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyNlYWYzZTFdIHJvdW5kZWQtbGcgdy0xMCBoLTEwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1yLTNcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtWyMzZDVjM2FdXCI+Vzwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0XCI+QUkgYnVpbGRlcjwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8bmF2PlxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9kYXNoYm9hcmRcIiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC04IHB5LTMgcm91bmRlZC1sLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgJHt0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUgPT09ICcvZGFzaGJvYXJkJyA/ICdiZy13aGl0ZSB0ZXh0LVsjM2Q1YzNhXSBmb250LXNlbWlib2xkIHNoYWRvdycgOiAnaG92ZXI6YmctWyM0ZTZlNGFdIGhvdmVyOnRleHQtd2hpdGUnfWB9PlxuICAgICAgICAgICAgICAgICAgPEdsb2JlIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtNFwiPldlYnNpdGVzPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkL2NyZWF0ZVwiIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTggcHktMyByb3VuZGVkLWwtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA9PT0gJy9kYXNoYm9hcmQvY3JlYXRlJyA/ICdiZy13aGl0ZSB0ZXh0LVsjM2Q1YzNhXSBmb250LXNlbWlib2xkIHNoYWRvdycgOiAnaG92ZXI6YmctWyM0ZTZlNGFdIGhvdmVyOnRleHQtd2hpdGUnfWB9PlxuICAgICAgICAgICAgICAgICAgPFBsdXNDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00XCI+Q3JlYXRlPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkL2RvbWFpblwiIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTggcHktMyByb3VuZGVkLWwtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA9PT0gJy9kYXNoYm9hcmQvZG9tYWluJyA/ICdiZy13aGl0ZSB0ZXh0LVsjM2Q1YzNhXSBmb250LXNlbWlib2xkIHNoYWRvdycgOiAnaG92ZXI6YmctWyM0ZTZlNGFdIGhvdmVyOnRleHQtd2hpdGUnfWB9PlxuICAgICAgICAgICAgICAgICAgPExpbmtJY29uIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtNFwiPkRvbWFpbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZC9hY2NvdW50XCIgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgcHgtOCBweS0zIHJvdW5kZWQtbC1mdWxsIHRyYW5zaXRpb24tY29sb3JzICR7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lID09PSAnL2Rhc2hib2FyZC9hY2NvdW50JyA/ICdiZy13aGl0ZSB0ZXh0LVsjM2Q1YzNhXSBmb250LXNlbWlib2xkIHNoYWRvdycgOiAnaG92ZXI6YmctWyM0ZTZlNGFdIGhvdmVyOnRleHQtd2hpdGUnfWB9PlxuICAgICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00XCI+QWNjb3VudDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZC9iaWxsaW5nXCIgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgcHgtOCBweS0zIHJvdW5kZWQtbC1mdWxsIHRyYW5zaXRpb24tY29sb3JzICR7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lID09PSAnL2Rhc2hib2FyZC9iaWxsaW5nJyA/ICdiZy13aGl0ZSB0ZXh0LVsjM2Q1YzNhXSBmb250LXNlbWlib2xkIHNoYWRvdycgOiAnaG92ZXI6YmctWyM0ZTZlNGFdIGhvdmVyOnRleHQtd2hpdGUnfWB9PlxuICAgICAgICAgICAgICAgICAgPENyZWRpdENhcmQgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00XCI+QmlsbGluZzwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZC9zZXR0aW5nc1wiIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTggcHktMyByb3VuZGVkLWwtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA9PT0gJy9kYXNoYm9hcmQvc2V0dGluZ3MnID8gJ2JnLXdoaXRlIHRleHQtWyMzZDVjM2FdIGZvbnQtc2VtaWJvbGQgc2hhZG93JyA6ICdob3ZlcjpiZy1bIzRlNmU0YV0gaG92ZXI6dGV4dC13aGl0ZSd9YH0+XG4gICAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00XCI+U2V0dGluZ3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L25hdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2FzaWRlPlxuXG4gICAgICB7LyogTW9iaWxlIFNpZGViYXIgKi99XG4gICAgICA8YXNpZGUgY2xhc3NOYW1lPXtgbGc6aGlkZGVuIGZpeGVkIGluc2V0LXktMCBsZWZ0LTAgei01MCB3LTY0IGJnLVsjM2Q1YzNhXSB0ZXh0LXdoaXRlIHRyYW5zZm9ybSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgZWFzZS1pbi1vdXQgJHtpc01vYmlsZU1lbnVPcGVuID8gJ3RyYW5zbGF0ZS14LTAnIDogJy10cmFuc2xhdGUteC1mdWxsJ31gfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbCBweS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcHgtOCBtYi0xMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLVsjZWFmM2UxXSByb3VuZGVkLWxnIHctMTAgaC0xMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtci0zXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtWyMzZDVjM2FdXCI+Vzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodFwiPkFJIGJ1aWxkZXI8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLW1kIGhvdmVyOmJnLVsjNGU2ZTRhXSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC04IHB5LTMgcm91bmRlZC1sLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgJHt0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUgPT09ICcvZGFzaGJvYXJkJyA/ICdiZy13aGl0ZSB0ZXh0LVsjM2Q1YzNhXSBmb250LXNlbWlib2xkIHNoYWRvdycgOiAnaG92ZXI6YmctWyM0ZTZlNGFdIGhvdmVyOnRleHQtd2hpdGUnfWB9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01vYmlsZU1lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8R2xvYmUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00XCI+V2Vic2l0ZXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkL2NyZWF0ZVwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC04IHB5LTMgcm91bmRlZC1sLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMgJHt0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cubG9jYXRpb24ucGF0aG5hbWUgPT09ICcvZGFzaGJvYXJkL2NyZWF0ZScgPyAnYmctd2hpdGUgdGV4dC1bIzNkNWMzYV0gZm9udC1zZW1pYm9sZCBzaGFkb3cnIDogJ2hvdmVyOmJnLVsjNGU2ZTRhXSBob3Zlcjp0ZXh0LXdoaXRlJ31gfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFBsdXNDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00XCI+Q3JlYXRlPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9kb21haW5cIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgcHgtOCBweS0zIHJvdW5kZWQtbC1mdWxsIHRyYW5zaXRpb24tY29sb3JzICR7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lID09PSAnL2Rhc2hib2FyZC9kb21haW4nID8gJ2JnLXdoaXRlIHRleHQtWyMzZDVjM2FdIGZvbnQtc2VtaWJvbGQgc2hhZG93JyA6ICdob3ZlcjpiZy1bIzRlNmU0YV0gaG92ZXI6dGV4dC13aGl0ZSd9YH1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxMaW5rSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTRcIj5Eb21haW48L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvZGFzaGJvYXJkL2FjY291bnRcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgcHgtOCBweS0zIHJvdW5kZWQtbC1mdWxsIHRyYW5zaXRpb24tY29sb3JzICR7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lID09PSAnL2Rhc2hib2FyZC9hY2NvdW50JyA/ICdiZy13aGl0ZSB0ZXh0LVsjM2Q1YzNhXSBmb250LXNlbWlib2xkIHNoYWRvdycgOiAnaG92ZXI6YmctWyM0ZTZlNGFdIGhvdmVyOnRleHQtd2hpdGUnfWB9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01vYmlsZU1lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTRcIj5BY2NvdW50PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICBocmVmPVwiL2Rhc2hib2FyZC9iaWxsaW5nXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHB4LTggcHktMyByb3VuZGVkLWwtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycyAke3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5sb2NhdGlvbi5wYXRobmFtZSA9PT0gJy9kYXNoYm9hcmQvYmlsbGluZycgPyAnYmctd2hpdGUgdGV4dC1bIzNkNWMzYV0gZm9udC1zZW1pYm9sZCBzaGFkb3cnIDogJ2hvdmVyOmJnLVsjNGU2ZTRhXSBob3Zlcjp0ZXh0LXdoaXRlJ31gfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPENyZWRpdENhcmQgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00XCI+QmlsbGluZzwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmQvc2V0dGluZ3NcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgcHgtOCBweS0zIHJvdW5kZWQtbC1mdWxsIHRyYW5zaXRpb24tY29sb3JzICR7dHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmxvY2F0aW9uLnBhdGhuYW1lID09PSAnL2Rhc2hib2FyZC9zZXR0aW5ncycgPyAnYmctd2hpdGUgdGV4dC1bIzNkNWMzYV0gZm9udC1zZW1pYm9sZCBzaGFkb3cnIDogJ2hvdmVyOmJnLVsjNGU2ZTRhXSBob3Zlcjp0ZXh0LXdoaXRlJ31gfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtNFwiPlNldHRpbmdzPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9uYXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9hc2lkZT5cblxuICAgICAgey8qIE1haW4gQ29udGVudCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBmbGV4LTEgbWluLWgtc2NyZWVuIG92ZXJmbG93LXgtYXV0byBsZzptbC02NFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInN0aWNreSB0b3AtMCB6LTEwIGJnLXdoaXRlIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGgtMTYgcHgtNCBzbTpweC02XCI+XG4gICAgICAgICAgICB7LyogTW9iaWxlIG1lbnUgYnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01vYmlsZU1lbnVPcGVuKHRydWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTYwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktMTAwIGxnOmhpZGRlblwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxNZW51IGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPlxuICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgIHsvKiBEZXNrdG9wIGhlYWRlciBjb250ZW50ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6YmxvY2tcIiAvPlxuXG4gICAgICAgICAgICB7LyogSGVhZGVyIGFjdGlvbnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzbTpzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJoaWRkZW4gcC0yIHJvdW5kZWQtZnVsbCBob3ZlcjpiZy1ncmF5LTEwMCBzbTpibG9ja1wiPlxuICAgICAgICAgICAgICAgIDxCZWxsIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTYwMCBzbTp3LTYgc206aC02XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiaGlkZGVuIHAtMiByb3VuZGVkLWZ1bGwgaG92ZXI6YmctZ3JheS0xMDAgc206YmxvY2tcIj5cbiAgICAgICAgICAgICAgICA8SGVscENpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JheS02MDAgc206dy02IHNtOmgtNlwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImhpZGRlbiBwLTIgcm91bmRlZC1mdWxsIGhvdmVyOmJnLWdyYXktMTAwIHNtOmJsb2NrXCI+XG4gICAgICAgICAgICAgICAgPFVzZXIgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyYXktNjAwIHNtOnctNiBzbTpoLTZcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgey8qIFNob3cgdXNlciBlbWFpbCAtIHJlc3BvbnNpdmUgKi99XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhpZGRlbiBtbC0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtZDpibG9jayBsZzp0ZXh0LWJhc2VcIj5cbiAgICAgICAgICAgICAgICB7bG9hZGluZyA/ICdMb2FkaW5nLi4uJyA6IHByb2ZpbGU/LmVtYWlsIHx8ICdOb3Qgc2lnbmVkIGluJ31cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICB7LyogU2lnbiBPdXQgQnV0dG9uIC0gcmVzcG9uc2l2ZSAqL31cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSB0ZXh0LXNtIHRleHQtd2hpdGUgYmctcmVkLTUwMCByb3VuZGVkIHNtOnB4LTQgc206cHktMiBob3ZlcjpiZy1yZWQtNjAwIGRpc2FibGVkOmJnLWdyYXktMzAwIGRpc2FibGVkOnRleHQtZ3JheS01MDAgc206dGV4dC1iYXNlXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXthc3luYyAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBzZXRTaWduaW5nT3V0KHRydWUpO1xuICAgICAgICAgICAgICAgICAgYXdhaXQgc2lnbk91dCgpO1xuICAgICAgICAgICAgICAgICAgc2V0U2lnbmluZ091dChmYWxzZSk7XG4gICAgICAgICAgICAgICAgICByb3V0ZXIucmVwbGFjZSgnL2xvZ2luJyk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17c2lnbmluZ091dH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzcGFuPlxuICAgICAgICAgICAgICAgICAge3NpZ25pbmdPdXQgPyAnU2lnbmluZyBvdXQuLi4nIDogJ1NpZ24gT3V0J31cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvaGVhZGVyPlxuXG4gICAgICAgIHsvKiBQYWdlIENvbnRlbnQgKi99XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBwLTQgb3ZlcmZsb3cteS1hdXRvIHNtOnAtNlwiPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59Il0sIm5hbWVzIjpbIkxpbmsiLCJSZWFjdCIsIkJlbGwiLCJIZWxwQ2lyY2xlIiwiVXNlciIsIkdsb2JlIiwiUGx1c0NpcmNsZSIsIlNldHRpbmdzIiwiQ3JlZGl0Q2FyZCIsIkxpbmtJY29uIiwiTWVudSIsIlgiLCJ1c2VSb3V0ZXIiLCJ1c2VBdXRoIiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJyb3V0ZXIiLCJwcm9maWxlIiwibG9hZGluZyIsInNpZ25PdXQiLCJzaWduaW5nT3V0Iiwic2V0U2lnbmluZ091dCIsInVzZVN0YXRlIiwiaXNNb2JpbGVNZW51T3BlbiIsInNldElzTW9iaWxlTWVudU9wZW4iLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwiYXNpZGUiLCJzcGFuIiwibmF2IiwidWwiLCJsaSIsImhyZWYiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInBhdGhuYW1lIiwiYnV0dG9uIiwiaGVhZGVyIiwiZW1haWwiLCJyZXBsYWNlIiwiZGlzYWJsZWQiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _MapDomainModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MapDomainModal */ \"(ssr)/./src/app/dashboard/MapDomainModal.tsx\");\n/* harmony import */ var _AskDomainModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AskDomainModal */ \"(ssr)/./src/app/dashboard/AskDomainModal.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/providers/AuthProvider */ \"(ssr)/./src/components/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst DashboardPage = ()=>{\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { user, loading } = (0,_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sitesLoading, setSitesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMapDomainOpen, setIsMapDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAskDomainOpen, setIsAskDomainOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSiteName, setSelectedSiteName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSiteId, setSelectedSiteId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const supabase = (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_2__.createClientComponentClient)();\n    // Helper function to format expiry date\n    const formatExpiryDate = (expiryTime)=>{\n        if (!expiryTime) return \"\";\n        try {\n            const date = new Date(expiryTime);\n            const now = new Date();\n            const formattedDate = date.toLocaleDateString(\"en-US\", {\n                month: \"short\",\n                day: \"numeric\",\n                year: \"numeric\"\n            });\n            if (date.getTime() <= now.getTime()) {\n                return `Expired ${formattedDate}`;\n            }\n            return `Expires ${formattedDate}`;\n        } catch  {\n            return \"\";\n        }\n    };\n    // Helper function to get tooltip text\n    const getTooltipText = (site)=>{\n        if (!site.expiry_time) return \"No expiry time set\";\n        const expiry = new Date(site.expiry_time);\n        const now = new Date();\n        const diffMs = expiry.getTime() - now.getTime();\n        if (diffMs <= 0) return \"Expired\";\n        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));\n        const diffHours = Math.floor(diffMs % (1000 * 60 * 60 * 24) / (1000 * 60 * 60));\n        return `${diffDays} days ${diffHours} hours left (expires at ${expiry.toLocaleString()})`;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !user) {\n            router.replace(\"/login\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Trigger AskDomainModal if redirected from checkout\n        const postCheckout = searchParams.get(\"postCheckout\");\n        const siteIdFromParam = searchParams.get(\"siteId\");\n        if (postCheckout && siteIdFromParam) {\n            setSelectedSiteId(siteIdFromParam);\n            setIsAskDomainOpen(true);\n        }\n    }, [\n        searchParams\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSites = async ()=>{\n            try {\n                const { data, error } = await supabase.from(\"user-websites\").select(\"id, site_name, expiry_status, expiry_time\"); // Fetch expiry_time\n                if (error) {\n                    throw error;\n                }\n                setSites(data);\n            } catch (err) {\n                console.error(\"Error fetching sites:\", err);\n                setError(err instanceof Error ? err.message : \"An unknown error occurred\");\n            } finally{\n                setSitesLoading(false);\n            }\n        };\n        fetchSites();\n    }, []);\n    if (loading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Checking authentication...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 104,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (sitesLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center\",\n            children: \"Loading sites...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 108,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 text-center text-red-500\",\n            children: [\n                \"Error: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n            lineNumber: 112,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col p-4 mb-4 space-y-4 bg-white rounded-lg shadow-md sm:flex-row sm:items-center sm:justify-between sm:p-6 sm:mb-6 sm:space-y-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-800 sm:text-2xl\",\n                        children: \"Websites\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full sm:w-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search websites...\",\n                                    className: \"w-full py-2 pl-10 pr-4 text-sm border rounded-md sm:w-64 focus:outline-none focus:ring-2 focus:ring-blue-500 sm:text-base\",\n                                    value: search,\n                                    onChange: (e)=>setSearch(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"absolute w-4 h-4 text-gray-400 transform -translate-y-1/2 sm:w-5 sm:h-5 left-3 top-1/2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: \"2\",\n                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 hidden overflow-hidden bg-white rounded-lg shadow-md md:block\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            scope: \"col\",\n                                            className: \"px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6\",\n                                            children: \"Site Name\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            scope: \"col\",\n                                            className: \"px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            scope: \"col\",\n                                            className: \"px-4 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase lg:px-6\",\n                                            children: \"Expiry\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: (search.trim() ? sites.filter((site)=>site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"flex items-center px-4 py-4 text-sm font-medium text-gray-900 lg:px-6 whitespace-nowrap\",\n                                                children: [\n                                                    site.site_name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"ml-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-4 text-sm font-medium text-right lg:px-6 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-end space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-blue-500 hover:text-blue-700\",\n                                                            title: \"Map Domain\",\n                                                            onClick: ()=>{\n                                                                setSelectedSiteName(site.site_name);\n                                                                setIsMapDomainOpen(true);\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"w-4 h-4 lg:w-5 lg:h-5\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        site.expiry_status === \"Temporary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-green-500 hover:text-green-700\",\n                                                            title: \"Choose Plan\",\n                                                            onClick: ()=>{\n                                                                window.location.href = `/payments?siteId=${site.id}`;\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"w-4 h-4 lg:w-5 lg:h-5\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        site.expiry_status === \"Permanent\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 text-purple-500 hover:text-purple-700\",\n                                                            title: \"Change Plan\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"w-4 h-4 lg:w-5 lg:h-5\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                        x: \"2\",\n                                                                        y: \"7\",\n                                                                        width: \"20\",\n                                                                        height: \"10\",\n                                                                        rx: \"2\",\n                                                                        ry: \"2\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        fill: \"none\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 214,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M2 11h20\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        cx: \"7\",\n                                                                        cy: \"15\",\n                                                                        r: \"1\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        cx: \"11\",\n                                                                        cy: \"15\",\n                                                                        r: \"1\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-4 py-4 text-sm lg:px-6\",\n                                                children: site.expiry_status === \"Temporary\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex px-2 text-xs font-semibold leading-5 text-gray-800 bg-gray-100 rounded-full w-fit\",\n                                                            title: getTooltipText(site),\n                                                            children: \"Temporary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        site.expiry_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `text-xs font-medium ${formatExpiryDate(site.expiry_time).startsWith(\"Expired\") ? \"text-red-500\" : \"text-gray-500\"}`,\n                                                            children: formatExpiryDate(site.expiry_time)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full w-fit\",\n                                                    children: \"Permanent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, site.id, true, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 text-sm text-gray-600 bg-white border-t border-gray-200 rounded-b-lg\",\n                        children: [\n                            sites.length,\n                            \" Sites\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 space-y-4 md:hidden\",\n                children: [\n                    (search.trim() ? sites.filter((site)=>site.site_name.toLowerCase().includes(search.toLowerCase())) : sites).map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-white rounded-lg shadow-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 truncate\",\n                                                    children: site.site_name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        stroke: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-end flex-shrink-0 ml-3 space-y-1\",\n                                            children: site.expiry_status === \"Temporary\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex px-2 text-xs font-semibold leading-5 text-gray-800 bg-gray-100 rounded-full\",\n                                                        title: getTooltipText(site),\n                                                        children: \"Temporary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    site.expiry_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `text-xs font-medium whitespace-nowrap ${formatExpiryDate(site.expiry_time).startsWith(\"Expired\") ? \"text-red-500\" : \"text-gray-500\"}`,\n                                                        children: formatExpiryDate(site.expiry_time)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full\",\n                                                children: \"Permanent\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-start pt-3 space-x-4 border-t border-gray-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center px-3 py-2 space-x-2 text-blue-500 transition-colors rounded-md hover:text-blue-700 hover:bg-blue-50\",\n                                            onClick: ()=>{\n                                                setSelectedSiteName(site.site_name);\n                                                setIsMapDomainOpen(true);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Map Domain\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        site.expiry_status === \"Temporary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center px-3 py-2 space-x-2 text-green-500 transition-colors rounded-md hover:text-green-700 hover:bg-green-50\",\n                                            onClick: ()=>{\n                                                window.location.href = `/payments?siteId=${site.id}`;\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Go Live\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        site.expiry_status === \"Permanent\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"flex items-center px-3 py-2 space-x-2 text-purple-500 transition-colors rounded-md hover:text-purple-700 hover:bg-purple-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                            x: \"2\",\n                                                            y: \"7\",\n                                                            width: \"20\",\n                                                            height: \"10\",\n                                                            rx: \"2\",\n                                                            ry: \"2\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            fill: \"none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M2 11h20\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"7\",\n                                                            cy: \"15\",\n                                                            r: \"1\",\n                                                            fill: \"currentColor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"11\",\n                                                            cy: \"15\",\n                                                            r: \"1\",\n                                                            fill: \"currentColor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Change Plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, site.id, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 text-sm text-center text-gray-600 bg-white rounded-lg shadow-md\",\n                        children: [\n                            sites.length,\n                            \" Sites\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MapDomainModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isMapDomainOpen,\n                onClose: ()=>setIsMapDomainOpen(false),\n                siteName: selectedSiteName\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AskDomainModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: isAskDomainOpen,\n                onYes: ()=>{\n                    // open map domain modal\n                    const site = sites.find((s)=>s.id === selectedSiteId);\n                    if (site) {\n                        setSelectedSiteName(site.site_name);\n                        setIsMapDomainOpen(true);\n                    }\n                    setIsAskDomainOpen(false);\n                },\n                onNo: ()=>{\n                    router.push(`/dashboard/domain`);\n                    setIsAskDomainOpen(false);\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch profile from public.profiles\n    const fetchProfile = async (email)=>{\n        const { data, error } = await supabase.from(\"profiles\").select(\"first_name, last_name, email\").eq(\"email\", email).single();\n        if (error) {\n            setProfile(null);\n            setError(error.message);\n        } else {\n            setProfile(data);\n        }\n    };\n    // Update profile in public.profiles\n    const updateProfile = async (updates)=>{\n        if (!profile) return;\n        setLoading(true);\n        const { error } = await supabase.from(\"profiles\").update(updates).eq(\"email\", profile.email);\n        if (error) {\n            setError(error.message);\n        } else {\n            setProfile({\n                ...profile,\n                ...updates\n            });\n        }\n        setLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getSession = async ()=>{\n            setLoading(true);\n            const { data, error } = await supabase.auth.getSession();\n            if (error) {\n                setError(error.message);\n                setSession(null);\n                setUser(null);\n                setProfile(null);\n            } else {\n                setSession(data.session);\n                setUser(data.session?.user ?? null);\n                if (data.session?.user?.email) {\n                    await fetchProfile(data.session.user.email);\n                } else {\n                    setProfile(null);\n                }\n            }\n            setLoading(false);\n        };\n        getSession();\n        const { data: listener } = supabase.auth.onAuthStateChange(async (_event, session)=>{\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user?.email) {\n                await fetchProfile(session.user.email);\n            } else {\n                setProfile(null);\n            }\n        });\n        return ()=>{\n            listener.subscription.unsubscribe();\n        };\n    }, []);\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        setError(null);\n        const { error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) setError(error.message);\n        setLoading(false);\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        setError(null);\n        const { error } = await supabase.auth.signOut();\n        if (error) setError(error.message);\n        setLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            profile,\n            setProfile,\n            updateProfile,\n            loading,\n            error,\n            signIn,\n            signOut\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd8cab21ee81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcz8yNTZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2Q4Y2FiMjFlZTgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/dashboard/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./styles/globals.css */ \"(rsc)/./src/app/styles/globals.css\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/providers/AuthProvider */ \"(rsc)/./src/components/providers/AuthProvider.tsx\");\n\n\n\nconst metadata = {\n    title: \"WordPress AI Builder\",\n    description: \"Automated WordPress site generation with AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNzQztBQUU3RCxNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQWlDO0lBQzVFLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ1IsNEVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnLi4vY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1dvcmRQcmVzcyBBSSBCdWlsZGVyJyxcbiAgZGVzY3JpcHRpb246ICdBdXRvbWF0ZWQgV29yZFByZXNzIHNpdGUgZ2VuZXJhdGlvbiB3aXRoIEFJJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/lucide-react","vendor-chunks/set-cookie-parser","vendor-chunks/jose"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();