/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/payments/page";
exports.ids = ["app/payments/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpayments%2Fpage&page=%2Fpayments%2Fpage&appPaths=%2Fpayments%2Fpage&pagePath=private-next-app-dir%2Fpayments%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpayments%2Fpage&page=%2Fpayments%2Fpage&appPaths=%2Fpayments%2Fpage&pagePath=private-next-app-dir%2Fpayments%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'payments',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payments/page.tsx */ \"(rsc)/./src/app/payments/page.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/payments/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/payments/page\",\n        pathname: \"/payments\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpayments%2Fpage&page=%2Fpayments%2Fpage&appPaths=%2Fpayments%2Fpage&pagePath=private-next-app-dir%2Fpayments%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fpayments%2Fpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fpayments%2Fpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/payments/page.tsx */ \"(ssr)/./src/app/payments/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGcGF5bWVudHMlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz85NzNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL3BheW1lbnRzL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fpayments%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(ssr)/./src/components/providers/AuthProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGc3R5bGVzJTJGZ2xvYmFscy5jc3MmbW9kdWxlcz0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGQXV0aFByb3ZpZGVyLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz8yNzE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/payments/page.tsx":
/*!***********************************!*\
  !*** ./src/app/payments/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\nconst PricingPage = ()=>{\n    const [isYearly, setIsYearly] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Change: Track loading per plan\n    const [loadingPlanId, setLoadingPlanId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const plans = [\n        {\n            id: \"starter\",\n            name: \"Starter\",\n            price: 5,\n            yearlyPrice: 3,\n            description: \"Perfect for individuals and small projects\",\n            features: [\n                \"10 GB CDN bandwidth\",\n                \"10 GB disk storage\",\n                \"Weekly backup schedule\",\n                \"Domain mapping\",\n                \"Standard support\",\n                \"SSL certificates\",\n                \"Basic analytics\"\n            ],\n            popular: false,\n            buttonText: \"Get Starter\",\n            buttonStyle: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white\"\n        },\n        {\n            id: \"pro\",\n            name: \"Pro\",\n            price: 29,\n            yearlyPrice: 24,\n            description: \"Ideal for growing businesses and agencies\",\n            features: [\n                \"75 GB CDN bandwidth\",\n                \"35 GB disk storage\",\n                \"Daily backup schedule\",\n                \"Domain mapping\",\n                \"Priority support\",\n                \"100GB storage\",\n                \"SSL certificates\",\n                \"Advanced analytics\",\n                \"Ecommerce availability\"\n            ],\n            popular: true,\n            buttonText: \"Get Pro\",\n            buttonStyle: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white\"\n        },\n        {\n            id: \"turbo\",\n            name: \"Turbo\",\n            price: 59,\n            yearlyPrice: 49,\n            description: \"For high-traffic sites and advanced teams\",\n            features: [\n                \"125 GB CDN bandwidth\",\n                \"50 GB disk storage\",\n                \"Daily backup schedule\",\n                \"Domain mapping\",\n                \"Dedicated support\",\n                \"SSL certificates\",\n                \"Advanced analytics\",\n                \"Custom integrations\",\n                \"SLA guarantee\",\n                \"Ecommerce availability\"\n            ],\n            popular: false,\n            buttonText: \"Get Turbo\",\n            buttonStyle: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white\"\n        },\n        {\n            id: \"enterprise\",\n            name: \"Enterprise\",\n            price: \"Custom\",\n            yearlyPrice: \"Custom\",\n            description: \"For large organizations with advanced needs\",\n            features: [\n                \"200 GB CDN bandwidth\",\n                \"75 GB disk storage\",\n                \"Daily backup schedule\",\n                \"Everything in Turbo\",\n                \"Custom integrations\",\n                \"Advanced security\",\n                \"Dedicated support\",\n                \"SLA guarantees\",\n                \"Ecommerce availability\"\n            ],\n            popular: false,\n            buttonText: \"Contact Sales\",\n            buttonStyle: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white\"\n        }\n    ];\n    const siteId =  false ? 0 : null;\n    const handlePlanSelect = async (planId)=>{\n        if (planId === \"enterprise\") {\n            window.location.href = \"mailto:<EMAIL>\";\n            return;\n        }\n        setLoadingPlanId(planId);\n        try {\n            // Get the current session token\n            const { data: { session } } = await supabase.auth.getSession();\n            if (!session) {\n                alert(\"Please log in to continue with payment.\");\n                window.location.href = \"/login\";\n                return;\n            }\n            const response = await fetch(\"/api/create-checkout-session\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${session.access_token}`\n                },\n                body: JSON.stringify({\n                    planId,\n                    isYearly,\n                    siteId\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to create checkout session\");\n            }\n            const { url } = await response.json();\n            window.location.href = url;\n        } catch (error) {\n            console.error(\"Error creating checkout session:\", error);\n            alert(\"Something went wrong. Please try again.\");\n            setLoadingPlanId(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen text-slate-800 bg-gradient-to-br from-green-50 to-green-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"px-4 py-8 mx-auto max-w-7xl sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"mb-4 text-4xl font-bold text-slate-800 md:text-6xl\",\n                            children: \"Pricing\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-8 text-xl text-slate-600\",\n                            children: \"Choose the plan that works for you\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center p-1 mb-12 bg-white border border-green-200 rounded-lg shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsYearly(false),\n                                    className: `px-6 py-2 rounded-md text-sm font-medium transition-all ${!isYearly ? \"bg-green-600 text-white shadow-sm\" : \"text-slate-600 hover:text-slate-800\"}`,\n                                    children: \"MONTHLY\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsYearly(true),\n                                    className: `px-6 py-2 rounded-md text-sm font-medium transition-all ${isYearly ? \"bg-green-600 text-white shadow-sm\" : \"text-slate-600 hover:text-slate-800\"}`,\n                                    children: [\n                                        \"YEARLY\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 ml-2 text-xs text-green-700 bg-green-100 rounded\",\n                                            children: \"SAVE 20%\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-8 text-2xl font-bold text-center text-slate-800\",\n                            children: \"Plans\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 gap-6 md:grid-cols-4\",\n                            children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `relative flex flex-col h-full min-h-[520px] rounded-2xl p-6 transition-all duration-300 ${plan.popular ? \"bg-white border-2 border-green-400 shadow-xl ring-2 ring-green-200\" : \"bg-white border border-green-200 shadow-sm\"} hover:scale-105 hover:shadow-xl hover:z-20 hover:border-green-400`,\n                                    style: {\n                                        boxSizing: \"border-box\"\n                                    },\n                                    children: [\n                                        plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute px-3 py-1 text-xs font-medium text-white rounded-full -top-3 left-6 bg-gradient-to-r from-green-600 to-green-700\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"mb-2 text-xl font-bold text-slate-800\",\n                                                    children: plan.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: plan.price === \"Custom\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-4xl font-bold text-slate-800\",\n                                                        children: \"Custom\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-baseline\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-4xl font-bold text-slate-800\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    isYearly ? plan.yearlyPrice : plan.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-1 text-slate-500\",\n                                                                children: \"/mo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            isYearly && typeof plan.yearlyPrice === \"number\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-xs text-slate-500\",\n                                                                children: [\n                                                                    \"(Billed yearly: $\",\n                                                                    plan.yearlyPrice * 12,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-6 text-sm text-slate-600\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"mb-4 text-sm font-medium text-slate-700\",\n                                                    children: \"Includes\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: plan.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"h-5 w-5 text-green-600 mr-3 mt-0.5 flex-shrink-0\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-slate-600\",\n                                                                    children: feature\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                                    lineNumber: 231,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handlePlanSelect(plan.id),\n                                                disabled: loadingPlanId === plan.id,\n                                                className: `w-full py-3 px-6 rounded-lg font-medium transition-all duration-200 ${loadingPlanId === plan.id ? \"bg-slate-400 cursor-not-allowed\" : plan.buttonStyle}`,\n                                                children: loadingPlanId === plan.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 mr-2 border-b-2 border-white rounded-full animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        \"Redirecting...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 23\n                                                }, undefined) : plan.buttonText\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, plan.id, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mb-8 text-2xl font-bold text-center text-slate-800\",\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                {\n                                    question: \"Can I change my plan at any time?\",\n                                    answer: \"Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.\"\n                                },\n                                {\n                                    question: \"Is there a free trial available?\",\n                                    answer: \"Yes, we offer a 14-day free trial for all paid plans. No credit card required to get started.\"\n                                },\n                                {\n                                    question: \"What payment methods do you accept?\",\n                                    answer: \"We accept all major credit cards, PayPal, and bank transfers for annual plans.\"\n                                },\n                                {\n                                    question: \"Do you offer refunds?\",\n                                    answer: \"Yes, we offer a 30-day money-back guarantee. If you're not satisfied, we'll refund your payment.\"\n                                }\n                            ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pb-6 border-b border-green-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"mb-2 text-lg font-semibold text-slate-800\",\n                                            children: faq.question\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: faq.answer\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PricingPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BheW1lbnRzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDZ0M7QUFDb0I7QUFFcEQsTUFBTUUsV0FBV0QsbUVBQVlBLENBQzNCRSwwQ0FBb0MsRUFDcENBLGtOQUF5QztBQUczQyxNQUFNSSxjQUFjO0lBQ2xCLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHVCwrQ0FBUUEsQ0FBQztJQUN6QyxpQ0FBaUM7SUFDakMsTUFBTSxDQUFDVSxlQUFlQyxpQkFBaUIsR0FBR1gsK0NBQVFBLENBQWdCO0lBRWxFLE1BQU1ZLFFBQVE7UUFDWjtZQUNFQyxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7WUFDYkMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLFNBQVM7WUFDVEMsWUFBWTtZQUNaQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFUixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7WUFDYkMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLFNBQVM7WUFDVEMsWUFBWTtZQUNaQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFUixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7WUFDYkMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLFNBQVM7WUFDVEMsWUFBWTtZQUNaQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFUixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLGFBQWE7WUFDYkMsVUFBVTtnQkFDUjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBQ0RDLFNBQVM7WUFDVEMsWUFBWTtZQUNaQyxhQUFhO1FBQ2Y7S0FDRDtJQUVELE1BQU1DLFNBQVMsTUFBa0IsR0FBYyxDQUFnRCxHQUFZO0lBRTNHLE1BQU1NLG1CQUFtQixPQUFPQztRQUM5QixJQUFJQSxXQUFXLGNBQWM7WUFDM0JMLE9BQU9DLFFBQVEsQ0FBQ0ssSUFBSSxHQUFHO1lBQ3ZCO1FBQ0Y7UUFFQW5CLGlCQUFpQmtCO1FBRWpCLElBQUk7WUFDRixnQ0FBZ0M7WUFDaEMsTUFBTSxFQUFFRSxNQUFNLEVBQUVDLE9BQU8sRUFBRSxFQUFFLEdBQUcsTUFBTTlCLFNBQVMrQixJQUFJLENBQUNDLFVBQVU7WUFDNUQsSUFBSSxDQUFDRixTQUFTO2dCQUNaRyxNQUFNO2dCQUNOWCxPQUFPQyxRQUFRLENBQUNLLElBQUksR0FBRztnQkFDdkI7WUFDRjtZQUVBLE1BQU1NLFdBQVcsTUFBTUMsTUFBTSxnQ0FBZ0M7Z0JBQzNEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtvQkFDaEIsaUJBQWlCLENBQUMsT0FBTyxFQUFFUCxRQUFRUSxZQUFZLENBQUMsQ0FBQztnQkFDbkQ7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJkO29CQUNBckI7b0JBQ0FjO2dCQUNGO1lBQ0Y7WUFFQSxJQUFJLENBQUNjLFNBQVNRLEVBQUUsRUFBRTtnQkFDaEIsTUFBTUMsWUFBWSxNQUFNVCxTQUFTVSxJQUFJO2dCQUNyQyxNQUFNLElBQUlDLE1BQU1GLFVBQVVHLEtBQUssSUFBSTtZQUNyQztZQUVBLE1BQU0sRUFBRUMsR0FBRyxFQUFFLEdBQUcsTUFBTWIsU0FBU1UsSUFBSTtZQUNuQ3RCLE9BQU9DLFFBQVEsQ0FBQ0ssSUFBSSxHQUFHbUI7UUFDekIsRUFBRSxPQUFPRCxPQUFPO1lBQ2RFLFFBQVFGLEtBQUssQ0FBQyxvQ0FBb0NBO1lBQ2xEYixNQUFNO1lBQ054QixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDd0M7UUFBSUMsV0FBVTtrQkFFYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQXFEOzs7Ozs7c0NBQ25FLDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBOEI7Ozs7OztzQ0FHM0MsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQ0NDLFNBQVMsSUFBTS9DLFlBQVk7b0NBQzNCMkMsV0FBVyxDQUFDLHdEQUF3RCxFQUNsRSxDQUFDNUMsV0FBVyxzQ0FBc0Msc0NBQ25ELENBQUM7OENBQ0g7Ozs7Ozs4Q0FHRCw4REFBQytDO29DQUNDQyxTQUFTLElBQU0vQyxZQUFZO29DQUMzQjJDLFdBQVcsQ0FBQyx3REFBd0QsRUFDbEU1QyxXQUFXLHNDQUFzQyxzQ0FDbEQsQ0FBQzs7d0NBQ0g7c0RBRUMsOERBQUNpRDs0Q0FBS0wsV0FBVTtzREFBNkQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNbkYsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ007NEJBQUdOLFdBQVU7c0NBQXFEOzs7Ozs7c0NBQ25FLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWnhDLE1BQU0rQyxHQUFHLENBQUMsQ0FBQ0MscUJBQ1YsOERBQUNUO29DQUVDQyxXQUFXLENBQUMsd0ZBQXdGLEVBQ2xHUSxLQUFLekMsT0FBTyxHQUNSLHVFQUNBLDZDQUNMLGtFQUFrRSxDQUFDO29DQUNwRTBDLE9BQU87d0NBQUVDLFdBQVc7b0NBQWE7O3dDQUVoQ0YsS0FBS3pDLE9BQU8sa0JBQ1gsOERBQUNnQzs0Q0FBSUMsV0FBVTtzREFBNEg7Ozs7OztzREFJN0ksOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ1c7b0RBQUdYLFdBQVU7OERBQXlDUSxLQUFLOUMsSUFBSTs7Ozs7OzhEQUNoRSw4REFBQ3FDO29EQUFJQyxXQUFVOzhEQUNaUSxLQUFLN0MsS0FBSyxLQUFLLHlCQUNkLDhEQUFDMEM7d0RBQUtMLFdBQVU7a0VBQW9DOzs7OztrRkFFcEQsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0s7Z0VBQUtMLFdBQVU7O29FQUFvQztvRUFDaEQ1QyxXQUFXb0QsS0FBSzVDLFdBQVcsR0FBRzRDLEtBQUs3QyxLQUFLOzs7Ozs7OzBFQUU1Qyw4REFBQzBDO2dFQUFLTCxXQUFVOzBFQUFzQjs7Ozs7OzREQUNyQzVDLFlBQVksT0FBT29ELEtBQUs1QyxXQUFXLEtBQUssMEJBQ3ZDLDhEQUFDeUM7Z0VBQUtMLFdBQVU7O29FQUE4QjtvRUFBa0JRLEtBQUs1QyxXQUFXLEdBQUc7b0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLOUYsOERBQUNzQztvREFBRUYsV0FBVTs4REFBK0JRLEtBQUszQyxXQUFXOzs7Ozs7Ozs7Ozs7c0RBRTlELDhEQUFDa0M7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDWTtvREFBR1osV0FBVTs4REFBMEM7Ozs7Ozs4REFDeEQsOERBQUNhO29EQUFHYixXQUFVOzhEQUNYUSxLQUFLMUMsUUFBUSxDQUFDeUMsR0FBRyxDQUFDLENBQUNPLFNBQVNDLHNCQUMzQiw4REFBQ0M7NERBQWVoQixXQUFVOzs4RUFDeEIsOERBQUNpQjtvRUFDQ2pCLFdBQVU7b0VBQ1ZrQixNQUFLO29FQUNMQyxTQUFROzhFQUVSLDRFQUFDQzt3RUFDQ0MsVUFBUzt3RUFDVEMsR0FBRTt3RUFDRkMsVUFBUzs7Ozs7Ozs7Ozs7OEVBR2IsOERBQUNsQjtvRUFBS0wsV0FBVTs4RUFBMEJjOzs7Ozs7OzJEQVpuQ0M7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBaUJmLDhEQUFDaEI7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNHO2dEQUNDQyxTQUFTLElBQU01QixpQkFBaUJnQyxLQUFLL0MsRUFBRTtnREFDdkMrRCxVQUFVbEUsa0JBQWtCa0QsS0FBSy9DLEVBQUU7Z0RBQ25DdUMsV0FBVyxDQUFDLG9FQUFvRSxFQUM5RTFDLGtCQUFrQmtELEtBQUsvQyxFQUFFLEdBQUcsb0NBQW9DK0MsS0FBS3ZDLFdBQVcsQ0FDakYsQ0FBQzswREFFRFgsa0JBQWtCa0QsS0FBSy9DLEVBQUUsaUJBQ3hCLDhEQUFDc0M7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7Ozs7O3dEQUF1RTs7Ozs7O2dFQUl4RlEsS0FBS3hDLFVBQVU7Ozs7Ozs7Ozs7OzttQ0FuRWhCd0MsS0FBSy9DLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBNkVwQiw4REFBQ3NDO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ007NEJBQUdOLFdBQVU7c0NBQXFEOzs7Ozs7c0NBRW5FLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWjtnQ0FDQztvQ0FDRXlCLFVBQVU7b0NBQ1ZDLFFBQ0U7Z0NBQ0o7Z0NBQ0E7b0NBQ0VELFVBQVU7b0NBQ1ZDLFFBQVE7Z0NBQ1Y7Z0NBQ0E7b0NBQ0VELFVBQVU7b0NBQ1ZDLFFBQVE7Z0NBQ1Y7Z0NBQ0E7b0NBQ0VELFVBQVU7b0NBQ1ZDLFFBQ0U7Z0NBQ0o7NkJBQ0QsQ0FBQ25CLEdBQUcsQ0FBQyxDQUFDb0IsS0FBS1osc0JBQ1YsOERBQUNoQjtvQ0FBZ0JDLFdBQVU7O3NEQUN6Qiw4REFBQ1c7NENBQUdYLFdBQVU7c0RBQTZDMkIsSUFBSUYsUUFBUTs7Ozs7O3NEQUN2RSw4REFBQ3ZCOzRDQUFFRixXQUFVO3NEQUFrQjJCLElBQUlELE1BQU07Ozs7Ozs7bUNBRmpDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXhCO0FBRUEsaUVBQWU1RCxXQUFXQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL3NyYy9hcHAvcGF5bWVudHMvcGFnZS50c3g/ZTlkYSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuXG5jb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChcbiAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhXG4pXG5cbmNvbnN0IFByaWNpbmdQYWdlID0gKCkgPT4ge1xuICBjb25zdCBbaXNZZWFybHksIHNldElzWWVhcmx5XSA9IHVzZVN0YXRlKGZhbHNlKVxuICAvLyBDaGFuZ2U6IFRyYWNrIGxvYWRpbmcgcGVyIHBsYW5cbiAgY29uc3QgW2xvYWRpbmdQbGFuSWQsIHNldExvYWRpbmdQbGFuSWRdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICBjb25zdCBwbGFucyA9IFtcbiAgICB7XG4gICAgICBpZDogXCJzdGFydGVyXCIsXG4gICAgICBuYW1lOiBcIlN0YXJ0ZXJcIixcbiAgICAgIHByaWNlOiA1LFxuICAgICAgeWVhcmx5UHJpY2U6IDMsXG4gICAgICBkZXNjcmlwdGlvbjogXCJQZXJmZWN0IGZvciBpbmRpdmlkdWFscyBhbmQgc21hbGwgcHJvamVjdHNcIixcbiAgICAgIGZlYXR1cmVzOiBbXG4gICAgICAgIFwiMTAgR0IgQ0ROIGJhbmR3aWR0aFwiLFxuICAgICAgICBcIjEwIEdCIGRpc2sgc3RvcmFnZVwiLFxuICAgICAgICBcIldlZWtseSBiYWNrdXAgc2NoZWR1bGVcIixcbiAgICAgICAgXCJEb21haW4gbWFwcGluZ1wiLFxuICAgICAgICBcIlN0YW5kYXJkIHN1cHBvcnRcIixcbiAgICAgICAgXCJTU0wgY2VydGlmaWNhdGVzXCIsXG4gICAgICAgIFwiQmFzaWMgYW5hbHl0aWNzXCIsXG4gICAgICBdLFxuICAgICAgcG9wdWxhcjogZmFsc2UsXG4gICAgICBidXR0b25UZXh0OiBcIkdldCBTdGFydGVyXCIsXG4gICAgICBidXR0b25TdHlsZTogXCJiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNjAwIHRvLWdyZWVuLTcwMCBob3Zlcjpmcm9tLWdyZWVuLTcwMCBob3Zlcjp0by1ncmVlbi04MDAgdGV4dC13aGl0ZVwiLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IFwicHJvXCIsXG4gICAgICBuYW1lOiBcIlByb1wiLFxuICAgICAgcHJpY2U6IDI5LFxuICAgICAgeWVhcmx5UHJpY2U6IDI0LFxuICAgICAgZGVzY3JpcHRpb246IFwiSWRlYWwgZm9yIGdyb3dpbmcgYnVzaW5lc3NlcyBhbmQgYWdlbmNpZXNcIixcbiAgICAgIGZlYXR1cmVzOiBbXG4gICAgICAgIFwiNzUgR0IgQ0ROIGJhbmR3aWR0aFwiLFxuICAgICAgICBcIjM1IEdCIGRpc2sgc3RvcmFnZVwiLFxuICAgICAgICBcIkRhaWx5IGJhY2t1cCBzY2hlZHVsZVwiLFxuICAgICAgICBcIkRvbWFpbiBtYXBwaW5nXCIsXG4gICAgICAgIFwiUHJpb3JpdHkgc3VwcG9ydFwiLFxuICAgICAgICBcIjEwMEdCIHN0b3JhZ2VcIixcbiAgICAgICAgXCJTU0wgY2VydGlmaWNhdGVzXCIsXG4gICAgICAgIFwiQWR2YW5jZWQgYW5hbHl0aWNzXCIsXG4gICAgICAgIFwiRWNvbW1lcmNlIGF2YWlsYWJpbGl0eVwiLFxuICAgICAgXSxcbiAgICAgIHBvcHVsYXI6IHRydWUsXG4gICAgICBidXR0b25UZXh0OiBcIkdldCBQcm9cIixcbiAgICAgIGJ1dHRvblN0eWxlOiBcImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi02MDAgdG8tZ3JlZW4tNzAwIGhvdmVyOmZyb20tZ3JlZW4tNzAwIGhvdmVyOnRvLWdyZWVuLTgwMCB0ZXh0LXdoaXRlXCIsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogXCJ0dXJib1wiLFxuICAgICAgbmFtZTogXCJUdXJib1wiLFxuICAgICAgcHJpY2U6IDU5LFxuICAgICAgeWVhcmx5UHJpY2U6IDQ5LFxuICAgICAgZGVzY3JpcHRpb246IFwiRm9yIGhpZ2gtdHJhZmZpYyBzaXRlcyBhbmQgYWR2YW5jZWQgdGVhbXNcIixcbiAgICAgIGZlYXR1cmVzOiBbXG4gICAgICAgIFwiMTI1IEdCIENETiBiYW5kd2lkdGhcIixcbiAgICAgICAgXCI1MCBHQiBkaXNrIHN0b3JhZ2VcIixcbiAgICAgICAgXCJEYWlseSBiYWNrdXAgc2NoZWR1bGVcIixcbiAgICAgICAgXCJEb21haW4gbWFwcGluZ1wiLFxuICAgICAgICBcIkRlZGljYXRlZCBzdXBwb3J0XCIsXG4gICAgICAgIFwiU1NMIGNlcnRpZmljYXRlc1wiLFxuICAgICAgICBcIkFkdmFuY2VkIGFuYWx5dGljc1wiLFxuICAgICAgICBcIkN1c3RvbSBpbnRlZ3JhdGlvbnNcIixcbiAgICAgICAgXCJTTEEgZ3VhcmFudGVlXCIsXG4gICAgICAgIFwiRWNvbW1lcmNlIGF2YWlsYWJpbGl0eVwiLFxuICAgICAgXSxcbiAgICAgIHBvcHVsYXI6IGZhbHNlLFxuICAgICAgYnV0dG9uVGV4dDogXCJHZXQgVHVyYm9cIixcbiAgICAgIGJ1dHRvblN0eWxlOiBcImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi02MDAgdG8tZ3JlZW4tNzAwIGhvdmVyOmZyb20tZ3JlZW4tNzAwIGhvdmVyOnRvLWdyZWVuLTgwMCB0ZXh0LXdoaXRlXCIsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogXCJlbnRlcnByaXNlXCIsXG4gICAgICBuYW1lOiBcIkVudGVycHJpc2VcIixcbiAgICAgIHByaWNlOiBcIkN1c3RvbVwiLFxuICAgICAgeWVhcmx5UHJpY2U6IFwiQ3VzdG9tXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJGb3IgbGFyZ2Ugb3JnYW5pemF0aW9ucyB3aXRoIGFkdmFuY2VkIG5lZWRzXCIsXG4gICAgICBmZWF0dXJlczogW1xuICAgICAgICBcIjIwMCBHQiBDRE4gYmFuZHdpZHRoXCIsXG4gICAgICAgIFwiNzUgR0IgZGlzayBzdG9yYWdlXCIsXG4gICAgICAgIFwiRGFpbHkgYmFja3VwIHNjaGVkdWxlXCIsXG4gICAgICAgIFwiRXZlcnl0aGluZyBpbiBUdXJib1wiLFxuICAgICAgICBcIkN1c3RvbSBpbnRlZ3JhdGlvbnNcIixcbiAgICAgICAgXCJBZHZhbmNlZCBzZWN1cml0eVwiLFxuICAgICAgICBcIkRlZGljYXRlZCBzdXBwb3J0XCIsXG4gICAgICAgIFwiU0xBIGd1YXJhbnRlZXNcIixcbiAgICAgICAgXCJFY29tbWVyY2UgYXZhaWxhYmlsaXR5XCIsXG4gICAgICBdLFxuICAgICAgcG9wdWxhcjogZmFsc2UsXG4gICAgICBidXR0b25UZXh0OiBcIkNvbnRhY3QgU2FsZXNcIixcbiAgICAgIGJ1dHRvblN0eWxlOiBcImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi02MDAgdG8tZ3JlZW4tNzAwIGhvdmVyOmZyb20tZ3JlZW4tNzAwIGhvdmVyOnRvLWdyZWVuLTgwMCB0ZXh0LXdoaXRlXCIsXG4gICAgfSxcbiAgXVxuXG4gIGNvbnN0IHNpdGVJZCA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gbmV3IFVSTFNlYXJjaFBhcmFtcyh3aW5kb3cubG9jYXRpb24uc2VhcmNoKS5nZXQoJ3NpdGVJZCcpIDogbnVsbFxuXG4gIGNvbnN0IGhhbmRsZVBsYW5TZWxlY3QgPSBhc3luYyAocGxhbklkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAocGxhbklkID09PSBcImVudGVycHJpc2VcIikge1xuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBcIm1haWx0bzpzYWxlc0B5b3VyY29tcGFueS5jb21cIlxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgc2V0TG9hZGluZ1BsYW5JZChwbGFuSWQpXG5cbiAgICB0cnkge1xuICAgICAgLy8gR2V0IHRoZSBjdXJyZW50IHNlc3Npb24gdG9rZW5cbiAgICAgIGNvbnN0IHsgZGF0YTogeyBzZXNzaW9uIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0U2Vzc2lvbigpO1xuICAgICAgaWYgKCFzZXNzaW9uKSB7XG4gICAgICAgIGFsZXJ0KFwiUGxlYXNlIGxvZyBpbiB0byBjb250aW51ZSB3aXRoIHBheW1lbnQuXCIpO1xuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IFwiL2xvZ2luXCI7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChcIi9hcGkvY3JlYXRlLWNoZWNrb3V0LXNlc3Npb25cIiwge1xuICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgICAgXCJBdXRob3JpemF0aW9uXCI6IGBCZWFyZXIgJHtzZXNzaW9uLmFjY2Vzc190b2tlbn1gXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBwbGFuSWQsXG4gICAgICAgICAgaXNZZWFybHksXG4gICAgICAgICAgc2l0ZUlkLFxuICAgICAgICB9KSxcbiAgICAgIH0pXG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmVycm9yIHx8IFwiRmFpbGVkIHRvIGNyZWF0ZSBjaGVja291dCBzZXNzaW9uXCIpXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHsgdXJsIH0gPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gdXJsXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBjcmVhdGluZyBjaGVja291dCBzZXNzaW9uOlwiLCBlcnJvcilcbiAgICAgIGFsZXJ0KFwiU29tZXRoaW5nIHdlbnQgd3JvbmcuIFBsZWFzZSB0cnkgYWdhaW4uXCIpXG4gICAgICBzZXRMb2FkaW5nUGxhbklkKG51bGwpXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiB0ZXh0LXNsYXRlLTgwMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTUwIHRvLWdyZWVuLTEwMFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS04IG14LWF1dG8gbWF4LXctN3hsIHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTEyIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cIm1iLTQgdGV4dC00eGwgZm9udC1ib2xkIHRleHQtc2xhdGUtODAwIG1kOnRleHQtNnhsXCI+UHJpY2luZzwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItOCB0ZXh0LXhsIHRleHQtc2xhdGUtNjAwXCI+Q2hvb3NlIHRoZSBwbGFuIHRoYXQgd29ya3MgZm9yIHlvdTwvcD5cblxuICAgICAgICAgIHsvKiBCaWxsaW5nIFRvZ2dsZSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBwLTEgbWItMTIgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmVlbi0yMDAgcm91bmRlZC1sZyBzaGFkb3ctc21cIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNZZWFybHkoZmFsc2UpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IHB5LTIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsICR7XG4gICAgICAgICAgICAgICAgIWlzWWVhcmx5ID8gXCJiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSBzaGFkb3ctc21cIiA6IFwidGV4dC1zbGF0ZS02MDAgaG92ZXI6dGV4dC1zbGF0ZS04MDBcIlxuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgTU9OVEhMWVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzWWVhcmx5KHRydWUpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC02IHB5LTIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsICR7XG4gICAgICAgICAgICAgICAgaXNZZWFybHkgPyBcImJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHNoYWRvdy1zbVwiIDogXCJ0ZXh0LXNsYXRlLTYwMCBob3Zlcjp0ZXh0LXNsYXRlLTgwMFwiXG4gICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBZRUFSTFlcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMiBweS0xIG1sLTIgdGV4dC14cyB0ZXh0LWdyZWVuLTcwMCBiZy1ncmVlbi0xMDAgcm91bmRlZFwiPlNBVkUgMjAlPC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcmljaW5nIFBsYW5zIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMTJcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwibWItOCB0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1jZW50ZXIgdGV4dC1zbGF0ZS04MDBcIj5QbGFuczwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGdhcC02IG1kOmdyaWQtY29scy00XCI+XG4gICAgICAgICAgICB7cGxhbnMubWFwKChwbGFuKSA9PiAoXG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBrZXk9e3BsYW4uaWR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgZmxleCBmbGV4LWNvbCBoLWZ1bGwgbWluLWgtWzUyMHB4XSByb3VuZGVkLTJ4bCBwLTYgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgICAgICAgICAgICBwbGFuLnBvcHVsYXJcbiAgICAgICAgICAgICAgICAgICAgPyBcImJnLXdoaXRlIGJvcmRlci0yIGJvcmRlci1ncmVlbi00MDAgc2hhZG93LXhsIHJpbmctMiByaW5nLWdyZWVuLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgIDogXCJiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyZWVuLTIwMCBzaGFkb3ctc21cIlxuICAgICAgICAgICAgICAgIH0gaG92ZXI6c2NhbGUtMTA1IGhvdmVyOnNoYWRvdy14bCBob3Zlcjp6LTIwIGhvdmVyOmJvcmRlci1ncmVlbi00MDBgfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IGJveFNpemluZzogXCJib3JkZXItYm94XCIgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtwbGFuLnBvcHVsYXIgJiYgKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBweC0zIHB5LTEgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCAtdG9wLTMgbGVmdC02IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi02MDAgdG8tZ3JlZW4tNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIE1vc3QgUG9wdWxhclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJtYi0yIHRleHQteGwgZm9udC1ib2xkIHRleHQtc2xhdGUtODAwXCI+e3BsYW4ubmFtZX08L2gzPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIHtwbGFuLnByaWNlID09PSBcIkN1c3RvbVwiID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTgwMFwiPkN1c3RvbTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtYmFzZWxpbmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAke2lzWWVhcmx5ID8gcGxhbi55ZWFybHlQcmljZSA6IHBsYW4ucHJpY2V9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0xIHRleHQtc2xhdGUtNTAwXCI+L21vPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAge2lzWWVhcmx5ICYmIHR5cGVvZiBwbGFuLnllYXJseVByaWNlID09PSAnbnVtYmVyJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPihCaWxsZWQgeWVhcmx5OiAke3BsYW4ueWVhcmx5UHJpY2UgKiAxMn0pPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItNiB0ZXh0LXNtIHRleHQtc2xhdGUtNjAwXCI+e3BsYW4uZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cIm1iLTQgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTcwMFwiPkluY2x1ZGVzPC9oND5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAge3BsYW4uZmVhdHVyZXMubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmVlbi02MDAgbXItMyBtdC0wLjUgZmxleC1zaHJpbmstMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDIwIDIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTYuNzA3IDUuMjkzYTEgMSAwIDAxMCAxLjQxNGwtOCA4YTEgMSAwIDAxLTEuNDE0IDBsLTQtNGExIDEgMCAwMTEuNDE0LTEuNDE0TDggMTIuNTg2bDcuMjkzLTcuMjkzYTEgMSAwIDAxMS40MTQgMHpcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsaXBSdWxlPVwiZXZlbm9kZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS02MDBcIj57ZmVhdHVyZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVQbGFuU2VsZWN0KHBsYW4uaWQpfVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ1BsYW5JZCA9PT0gcGxhbi5pZH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB5LTMgcHgtNiByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgICAgIGxvYWRpbmdQbGFuSWQgPT09IHBsYW4uaWQgPyBcImJnLXNsYXRlLTQwMCBjdXJzb3Itbm90LWFsbG93ZWRcIiA6IHBsYW4uYnV0dG9uU3R5bGVcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nUGxhbklkID09PSBwbGFuLmlkID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yIGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlIHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW5cIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIFJlZGlyZWN0aW5nLi4uXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgcGxhbi5idXR0b25UZXh0XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZBUSBTZWN0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cIm1iLTggdGV4dC0yeGwgZm9udC1ib2xkIHRleHQtY2VudGVyIHRleHQtc2xhdGUtODAwXCI+RnJlcXVlbnRseSBBc2tlZCBRdWVzdGlvbnM8L2gyPlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIHtbXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICBxdWVzdGlvbjogXCJDYW4gSSBjaGFuZ2UgbXkgcGxhbiBhdCBhbnkgdGltZT9cIixcbiAgICAgICAgICAgICAgICBhbnN3ZXI6XG4gICAgICAgICAgICAgICAgICBcIlllcywgeW91IGNhbiB1cGdyYWRlIG9yIGRvd25ncmFkZSB5b3VyIHBsYW4gYXQgYW55IHRpbWUuIENoYW5nZXMgd2lsbCBiZSByZWZsZWN0ZWQgaW4geW91ciBuZXh0IGJpbGxpbmcgY3ljbGUuXCIsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICBxdWVzdGlvbjogXCJJcyB0aGVyZSBhIGZyZWUgdHJpYWwgYXZhaWxhYmxlP1wiLFxuICAgICAgICAgICAgICAgIGFuc3dlcjogXCJZZXMsIHdlIG9mZmVyIGEgMTQtZGF5IGZyZWUgdHJpYWwgZm9yIGFsbCBwYWlkIHBsYW5zLiBObyBjcmVkaXQgY2FyZCByZXF1aXJlZCB0byBnZXQgc3RhcnRlZC5cIixcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIHF1ZXN0aW9uOiBcIldoYXQgcGF5bWVudCBtZXRob2RzIGRvIHlvdSBhY2NlcHQ/XCIsXG4gICAgICAgICAgICAgICAgYW5zd2VyOiBcIldlIGFjY2VwdCBhbGwgbWFqb3IgY3JlZGl0IGNhcmRzLCBQYXlQYWwsIGFuZCBiYW5rIHRyYW5zZmVycyBmb3IgYW5udWFsIHBsYW5zLlwiLFxuICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgcXVlc3Rpb246IFwiRG8geW91IG9mZmVyIHJlZnVuZHM/XCIsXG4gICAgICAgICAgICAgICAgYW5zd2VyOlxuICAgICAgICAgICAgICAgICAgXCJZZXMsIHdlIG9mZmVyIGEgMzAtZGF5IG1vbmV5LWJhY2sgZ3VhcmFudGVlLiBJZiB5b3UncmUgbm90IHNhdGlzZmllZCwgd2UnbGwgcmVmdW5kIHlvdXIgcGF5bWVudC5cIixcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIF0ubWFwKChmYXEsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwicGItNiBib3JkZXItYiBib3JkZXItZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cIm1iLTIgdGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtc2xhdGUtODAwXCI+e2ZhcS5xdWVzdGlvbn08L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNjAwXCI+e2ZhcS5hbnN3ZXJ9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IFByaWNpbmdQYWdlIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2UiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJQcmljaW5nUGFnZSIsImlzWWVhcmx5Iiwic2V0SXNZZWFybHkiLCJsb2FkaW5nUGxhbklkIiwic2V0TG9hZGluZ1BsYW5JZCIsInBsYW5zIiwiaWQiLCJuYW1lIiwicHJpY2UiLCJ5ZWFybHlQcmljZSIsImRlc2NyaXB0aW9uIiwiZmVhdHVyZXMiLCJwb3B1bGFyIiwiYnV0dG9uVGV4dCIsImJ1dHRvblN0eWxlIiwic2l0ZUlkIiwiVVJMU2VhcmNoUGFyYW1zIiwid2luZG93IiwibG9jYXRpb24iLCJzZWFyY2giLCJnZXQiLCJoYW5kbGVQbGFuU2VsZWN0IiwicGxhbklkIiwiaHJlZiIsImRhdGEiLCJzZXNzaW9uIiwiYXV0aCIsImdldFNlc3Npb24iLCJhbGVydCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYWNjZXNzX3Rva2VuIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvayIsImVycm9yRGF0YSIsImpzb24iLCJFcnJvciIsImVycm9yIiwidXJsIiwiY29uc29sZSIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcGFuIiwiaDIiLCJtYXAiLCJwbGFuIiwic3R5bGUiLCJib3hTaXppbmciLCJoMyIsImg0IiwidWwiLCJmZWF0dXJlIiwiaW5kZXgiLCJsaSIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwiZGlzYWJsZWQiLCJxdWVzdGlvbiIsImFuc3dlciIsImZhcSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/payments/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch profile from public.profiles\n    const fetchProfile = async (email)=>{\n        const { data, error } = await supabase.from(\"profiles\").select(\"*\").eq(\"email\", email).single();\n        if (error) {\n            setProfile(null);\n            setError(error.message);\n        } else {\n            setProfile(data);\n        }\n    };\n    // Update profile in public.profiles\n    const updateProfile = async (updates)=>{\n        if (!profile) return;\n        setLoading(true);\n        try {\n            const { data, error } = await supabase.from(\"profiles\").update(updates).eq(\"email\", profile.email).select().single();\n            if (error) {\n                setError(error.message);\n                throw error;\n            }\n            setProfile({\n                ...profile,\n                ...data\n            });\n            return data;\n        } catch (error) {\n            console.error(\"Error updating profile:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getSession = async ()=>{\n            setLoading(true);\n            const { data, error } = await supabase.auth.getSession();\n            if (error) {\n                setError(error.message);\n                setSession(null);\n                setUser(null);\n                setProfile(null);\n            } else {\n                setSession(data.session);\n                setUser(data.session?.user ?? null);\n                if (data.session?.user?.email) {\n                    await fetchProfile(data.session.user.email);\n                } else {\n                    setProfile(null);\n                }\n            }\n            setLoading(false);\n        };\n        getSession();\n        const { data: listener } = supabase.auth.onAuthStateChange(async (_event, session)=>{\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user?.email) {\n                await fetchProfile(session.user.email);\n            } else {\n                setProfile(null);\n            }\n        });\n        return ()=>{\n            listener.subscription.unsubscribe();\n        };\n    }, []);\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        setError(null);\n        const { error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) setError(error.message);\n        setLoading(false);\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        setError(null);\n        const { error } = await supabase.auth.signOut();\n        if (error) setError(error.message);\n        setLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            profile,\n            setProfile,\n            updateProfile,\n            loading,\n            error,\n            signIn,\n            signOut\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd8cab21ee81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcz8yNTZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2Q4Y2FiMjFlZTgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./styles/globals.css */ \"(rsc)/./src/app/styles/globals.css\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/providers/AuthProvider */ \"(rsc)/./src/components/providers/AuthProvider.tsx\");\n\n\n\nconst metadata = {\n    title: \"WordPress AI Builder\",\n    description: \"Automated WordPress site generation with AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNzQztBQUU3RCxNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQWlDO0lBQzVFLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ1IsNEVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnLi4vY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1dvcmRQcmVzcyBBSSBCdWlsZGVyJyxcbiAgZGVzY3JpcHRpb246ICdBdXRvbWF0ZWQgV29yZFByZXNzIHNpdGUgZ2VuZXJhdGlvbiB3aXRoIEFJJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/payments/page.tsx":
/*!***********************************!*\
  !*** ./src/app/payments/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/payments/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpayments%2Fpage&page=%2Fpayments%2Fpage&appPaths=%2Fpayments%2Fpage&pagePath=private-next-app-dir%2Fpayments%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();