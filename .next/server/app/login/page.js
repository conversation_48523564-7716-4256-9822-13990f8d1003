/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Flogin%2Fpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Flogin%2Fpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGbG9naW4lMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz8yNmIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvYXBwL2xvZ2luL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Flogin%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(ssr)/./src/components/providers/AuthProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGaG9tZSUyRmRlbGwlMkZEZXNrdG9wJTJGd3AtYWktYXBwJTJGc3JjJTJGYXBwJTJGc3R5bGVzJTJGZ2xvYmFscy5jc3MmbW9kdWxlcz0lMkZob21lJTJGZGVsbCUyRkRlc2t0b3AlMkZ3cC1haS1hcHAlMkZzcmMlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGQXV0aFByb3ZpZGVyLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLz8yNzE4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvZGVsbC9EZXNrdG9wL3dwLWFpLWFwcC9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp%2Fstyles%2Fglobals.css&modules=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fcomponents%2Fproviders%2FAuthProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/components/EyeIcons.tsx":
/*!*****************************************!*\
  !*** ./src/app/components/EyeIcons.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EyeClosedIcon: () => (/* binding */ EyeClosedIcon),\n/* harmony export */   EyeOpenIcon: () => (/* binding */ EyeOpenIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst EyeOpenIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/components/EyeIcons.tsx\",\n                lineNumber: 5,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"3\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/components/EyeIcons.tsx\",\n                lineNumber: 6,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/components/EyeIcons.tsx\",\n        lineNumber: 4,\n        columnNumber: 3\n    }, undefined);\nconst EyeClosedIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"20\",\n        height: \"20\",\n        viewBox: \"0 0 24 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/components/EyeIcons.tsx\",\n                lineNumber: 12,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                x1: \"1\",\n                y1: \"1\",\n                x2: \"23\",\n                y2: \"23\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/components/EyeIcons.tsx\",\n                lineNumber: 13,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/components/EyeIcons.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/EyeIcons.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _components_EyeIcons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/EyeIcons */ \"(ssr)/./src/app/components/EyeIcons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\nfunction LoginPage() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const { data: authListener } = supabase.auth.onAuthStateChange((event, session)=>{\n            if (session) {\n                window.location.href = \"/dashboard\";\n            }\n        });\n        return ()=>{\n            authListener.subscription.unsubscribe();\n        };\n    }, []);\n    const handleOAuthLogin = async (provider)=>{\n        await supabase.auth.signInWithOAuth({\n            provider\n        });\n    };\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        const { error, data } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) setError(error.message);\n        else window.location.href = \"/dashboard\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            background: \"linear-gradient(135deg, #f5f7fa 0%, #e8f5e9 100%)\",\n            padding: 16\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                background: \"white\",\n                padding: \"48px 32px\",\n                borderRadius: \"16px\",\n                boxShadow: \"0 8px 32px 0 rgba(31, 38, 135, 0.10)\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                alignItems: \"center\",\n                width: \"100%\",\n                maxWidth: 500,\n                minWidth: 0,\n                border: \"1.5px solid #1DBF73\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    style: {\n                        marginBottom: 8,\n                        fontWeight: 700,\n                        fontSize: 26,\n                        color: \"#1a3c34\"\n                    },\n                    children: \"Welcome to WP AI\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        width: \"100%\",\n                        margin: \"24px 0 12px 0\",\n                        display: \"flex\",\n                        gap: 8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleOAuthLogin(\"google\"),\n                            style: {\n                                flex: 1,\n                                background: \"#fff\",\n                                border: \"1px solid #e0e0e0\",\n                                borderRadius: 8,\n                                padding: 8,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                gap: 8,\n                                fontWeight: 600,\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"18\",\n                                    height: \"18\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"#4285F4\",\n                                            d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"#34A853\",\n                                            d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"#FBBC05\",\n                                            d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"#EA4335\",\n                                            d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 11\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Google\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleOAuthLogin(\"apple\"),\n                            style: {\n                                flex: 1,\n                                background: \"#fff\",\n                                border: \"1px solid #e0e0e0\",\n                                borderRadius: 8,\n                                padding: 8,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                gap: 8,\n                                fontWeight: 600,\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"18\",\n                                    height: \"18\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fill: \"#000\",\n                                        d: \"M17.05 20.28c-.98.95-2.05.8-3.08.35-1.09-.46-2.09-.48-3.24 0-1.44.62-2.2.44-3.06-.35C2.79 15.25 3.51 7.59 9.05 7.31c1.35.07 2.29.74 3.08.8 1.18-.24 2.31-.93 3.57-.84 1.51.12 2.65.72 3.4 1.8-3.12 1.87-2.38 5.98.48 7.13-.57 1.5-1.31 2.99-2.54 4.09l.01-.01zM12.03 7.25c-.15-2.23 1.66-4.07 3.74-4.25.29 2.58-2.34 4.5-3.74 4.25z\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 11\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Apple\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleOAuthLogin(\"azure\"),\n                            style: {\n                                flex: 1,\n                                background: \"#fff\",\n                                border: \"1px solid #e0e0e0\",\n                                borderRadius: 8,\n                                padding: 8,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\",\n                                gap: 8,\n                                fontWeight: 600,\n                                cursor: \"pointer\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"18\",\n                                    height: \"18\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"#F25022\",\n                                            d: \"M11.4 11.4H2.6V2.6h8.8v8.8z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"#00A4EF\",\n                                            d: \"M21.4 11.4h-8.8V2.6h8.8v8.8z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"#7FBA00\",\n                                            d: \"M11.4 21.4H2.6v-8.8h8.8v8.8z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fill: \"#FFB900\",\n                                            d: \"M21.4 21.4h-8.8v-8.8h8.8v8.8z\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 11\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Microsoft\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        width: \"100%\",\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        margin: \"16px 0 8px 0\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flex: 1,\n                                height: 1,\n                                background: \"#e0e0e0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                margin: \"0 12px\",\n                                color: \"#aaa\",\n                                fontSize: 14\n                            },\n                            children: \"OR\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flex: 1,\n                                height: 1,\n                                background: \"#e0e0e0\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    style: {\n                        width: \"100%\",\n                        marginTop: 8\n                    },\n                    onSubmit: handleLogin,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: 16\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        fontWeight: 500,\n                                        color: \"#222\",\n                                        fontSize: 15\n                                    },\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"email\",\n                                    value: email,\n                                    onChange: (e)=>setEmail(e.target.value),\n                                    required: true,\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"10px 12px\",\n                                        border: \"1px solid #e0e0e0\",\n                                        borderRadius: 8,\n                                        marginTop: 6,\n                                        fontSize: 15\n                                    },\n                                    placeholder: \"Enter your email\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: 8,\n                                position: \"relative\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        fontWeight: 500,\n                                        color: \"#222\",\n                                        fontSize: 15\n                                    },\n                                    children: \"Password\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: showPassword ? \"text\" : \"password\",\n                                    value: password,\n                                    onChange: (e)=>setPassword(e.target.value),\n                                    required: true,\n                                    style: {\n                                        width: \"100%\",\n                                        padding: \"10px 12px\",\n                                        border: \"1px solid #e0e0e0\",\n                                        borderRadius: 8,\n                                        marginTop: 6,\n                                        fontSize: 15\n                                    },\n                                    placeholder: \"Enter your password\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    onClick: ()=>setShowPassword((v)=>!v),\n                                    style: {\n                                        position: \"absolute\",\n                                        right: 12,\n                                        top: 38,\n                                        cursor: \"pointer\",\n                                        color: \"#888\"\n                                    },\n                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EyeIcons__WEBPACK_IMPORTED_MODULE_2__.EyeClosedIcon, {}, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 31\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_EyeIcons__WEBPACK_IMPORTED_MODULE_2__.EyeOpenIcon, {}, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 51\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\",\n                                marginBottom: 16\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        fontSize: 14,\n                                        color: \"#555\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            style: {\n                                                marginRight: 6\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Remember me\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    style: {\n                                        color: \"#1DBF73\",\n                                        fontWeight: 500,\n                                        fontSize: 14,\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Forgot Password?\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: \"#e53935\",\n                                marginBottom: 8,\n                                fontSize: 14\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            style: {\n                                width: \"100%\",\n                                padding: \"12px 0\",\n                                background: \"#1DBF73\",\n                                color: \"white\",\n                                border: \"none\",\n                                borderRadius: 8,\n                                fontSize: 16,\n                                fontWeight: 600,\n                                marginBottom: 8,\n                                cursor: \"pointer\"\n                            },\n                            children: \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginTop: 8,\n                        color: \"#444\",\n                        fontSize: 15\n                    },\n                    children: [\n                        \"Don't have an account? \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/signup\",\n                            style: {\n                                color: \"#1DBF73\",\n                                fontWeight: 600,\n                                textDecoration: \"none\"\n                            },\n                            children: \"Sign Up\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 39\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xvZ2luL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ1M7QUFDZTtBQUVwRSxNQUFNSyxXQUFXSCxtRUFBWUEsQ0FDM0JJLDBDQUFvQyxFQUNwQ0Esa05BQXlDO0FBRzVCLFNBQVNJO0lBQ3RCLE1BQU0sQ0FBQ0MsT0FBT0MsU0FBUyxHQUFHWCwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNZLFVBQVVDLFlBQVksR0FBR2IsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDYyxjQUFjQyxnQkFBZ0IsR0FBR2YsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDZ0IsT0FBT0MsU0FBUyxHQUFHakIsK0NBQVFBLENBQUM7SUFFbkNELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTSxFQUFFbUIsTUFBTUMsWUFBWSxFQUFFLEdBQUdmLFNBQVNnQixJQUFJLENBQUNDLGlCQUFpQixDQUFDLENBQUNDLE9BQU9DO1lBQ3JFLElBQUlBLFNBQVM7Z0JBQ1hDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO1lBQ3pCO1FBQ0Y7UUFDQSxPQUFPO1lBQ0xQLGFBQWFRLFlBQVksQ0FBQ0MsV0FBVztRQUN2QztJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1DLG1CQUFtQixPQUFPQztRQUM5QixNQUFNMUIsU0FBU2dCLElBQUksQ0FBQ1csZUFBZSxDQUFDO1lBQUVEO1FBQVM7SUFDakQ7SUFFQSxNQUFNRSxjQUFjLE9BQU9DO1FBQ3pCQSxFQUFFQyxjQUFjO1FBQ2hCakIsU0FBUztRQUNULE1BQU0sRUFBRUQsS0FBSyxFQUFFRSxJQUFJLEVBQUUsR0FBRyxNQUFNZCxTQUFTZ0IsSUFBSSxDQUFDZSxrQkFBa0IsQ0FBQztZQUFFekI7WUFBT0U7UUFBUztRQUNqRixJQUFJSSxPQUFPQyxTQUFTRCxNQUFNb0IsT0FBTzthQUM1QlosT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7SUFDOUI7SUFFQSxxQkFDRSw4REFBQ1c7UUFBSUMsT0FBTztZQUNWQyxXQUFXO1lBQ1hDLFNBQVM7WUFDVEMsWUFBWTtZQUNaQyxnQkFBZ0I7WUFDaEJDLFlBQVk7WUFDWkMsU0FBUztRQUNYO2tCQUNFLDRFQUFDUDtZQUFJQyxPQUFPO2dCQUNWSyxZQUFZO2dCQUNaQyxTQUFTO2dCQUNUQyxjQUFjO2dCQUNkQyxXQUFXO2dCQUNYTixTQUFTO2dCQUNUTyxlQUFlO2dCQUNmTixZQUFZO2dCQUNaTyxPQUFPO2dCQUNQQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxRQUFRO1lBQ1Y7OzhCQUNFLDhEQUFDQztvQkFBR2QsT0FBTzt3QkFBRWUsY0FBYzt3QkFBR0MsWUFBWTt3QkFBS0MsVUFBVTt3QkFBSUMsT0FBTztvQkFBVTs4QkFBRzs7Ozs7OzhCQUNqRiw4REFBQ25CO29CQUFJQyxPQUFPO3dCQUFFVSxPQUFPO3dCQUFRUyxRQUFRO3dCQUFpQmpCLFNBQVM7d0JBQVFrQixLQUFLO29CQUFFOztzQ0FDNUUsOERBQUNDOzRCQUFPQyxTQUFTLElBQU0vQixpQkFBaUI7NEJBQ3RDUyxPQUFPO2dDQUFFdUIsTUFBTTtnQ0FBR2xCLFlBQVk7Z0NBQVFRLFFBQVE7Z0NBQXFCTixjQUFjO2dDQUFHRCxTQUFTO2dDQUFHSixTQUFTO2dDQUFRQyxZQUFZO2dDQUFVQyxnQkFBZ0I7Z0NBQVVnQixLQUFLO2dDQUFHSixZQUFZO2dDQUFLUSxRQUFROzRCQUFVOzs4Q0FDOU0sOERBQUNDO29DQUFJZixPQUFNO29DQUFLZ0IsUUFBTztvQ0FBS0MsU0FBUTs7c0RBQ2xDLDhEQUFDQzs0Q0FBS0MsTUFBSzs0Q0FBVUMsR0FBRTs7Ozs7O3NEQUN2Qiw4REFBQ0Y7NENBQUtDLE1BQUs7NENBQVVDLEdBQUU7Ozs7OztzREFDdkIsOERBQUNGOzRDQUFLQyxNQUFLOzRDQUFVQyxHQUFFOzs7Ozs7c0RBQ3ZCLDhEQUFDRjs0Q0FBS0MsTUFBSzs0Q0FBVUMsR0FBRTs7Ozs7Ozs7Ozs7O2dDQUNuQjs4Q0FBWSw4REFBQ0M7OENBQUs7Ozs7Ozs7Ozs7OztzQ0FFeEIsOERBQUNWOzRCQUFPQyxTQUFTLElBQU0vQixpQkFBaUI7NEJBQ3RDUyxPQUFPO2dDQUFFdUIsTUFBTTtnQ0FBR2xCLFlBQVk7Z0NBQVFRLFFBQVE7Z0NBQXFCTixjQUFjO2dDQUFHRCxTQUFTO2dDQUFHSixTQUFTO2dDQUFRQyxZQUFZO2dDQUFVQyxnQkFBZ0I7Z0NBQVVnQixLQUFLO2dDQUFHSixZQUFZO2dDQUFLUSxRQUFROzRCQUFVOzs4Q0FDOU0sOERBQUNDO29DQUFJZixPQUFNO29DQUFLZ0IsUUFBTztvQ0FBS0MsU0FBUTs4Q0FDbEMsNEVBQUNDO3dDQUFLQyxNQUFLO3dDQUFPQyxHQUFFOzs7Ozs7Ozs7OztnQ0FDaEI7OENBQVksOERBQUNDOzhDQUFLOzs7Ozs7Ozs7Ozs7c0NBRXhCLDhEQUFDVjs0QkFBT0MsU0FBUyxJQUFNL0IsaUJBQWlCOzRCQUN0Q1MsT0FBTztnQ0FBRXVCLE1BQU07Z0NBQUdsQixZQUFZO2dDQUFRUSxRQUFRO2dDQUFxQk4sY0FBYztnQ0FBR0QsU0FBUztnQ0FBR0osU0FBUztnQ0FBUUMsWUFBWTtnQ0FBVUMsZ0JBQWdCO2dDQUFVZ0IsS0FBSztnQ0FBR0osWUFBWTtnQ0FBS1EsUUFBUTs0QkFBVTs7OENBQzlNLDhEQUFDQztvQ0FBSWYsT0FBTTtvQ0FBS2dCLFFBQU87b0NBQUtDLFNBQVE7O3NEQUNsQyw4REFBQ0M7NENBQUtDLE1BQUs7NENBQVVDLEdBQUU7Ozs7OztzREFDdkIsOERBQUNGOzRDQUFLQyxNQUFLOzRDQUFVQyxHQUFFOzs7Ozs7c0RBQ3ZCLDhEQUFDRjs0Q0FBS0MsTUFBSzs0Q0FBVUMsR0FBRTs7Ozs7O3NEQUN2Qiw4REFBQ0Y7NENBQUtDLE1BQUs7NENBQVVDLEdBQUU7Ozs7Ozs7Ozs7OztnQ0FDbkI7OENBQVksOERBQUNDOzhDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBRzFCLDhEQUFDaEM7b0JBQUlDLE9BQU87d0JBQUVVLE9BQU87d0JBQVFSLFNBQVM7d0JBQVFDLFlBQVk7d0JBQVVnQixRQUFRO29CQUFlOztzQ0FDekYsOERBQUNwQjs0QkFBSUMsT0FBTztnQ0FBRXVCLE1BQU07Z0NBQUdHLFFBQVE7Z0NBQUdyQixZQUFZOzRCQUFVOzs7Ozs7c0NBQ3hELDhEQUFDMEI7NEJBQUsvQixPQUFPO2dDQUFFbUIsUUFBUTtnQ0FBVUQsT0FBTztnQ0FBUUQsVUFBVTs0QkFBRztzQ0FBRzs7Ozs7O3NDQUNoRSw4REFBQ2xCOzRCQUFJQyxPQUFPO2dDQUFFdUIsTUFBTTtnQ0FBR0csUUFBUTtnQ0FBR3JCLFlBQVk7NEJBQVU7Ozs7Ozs7Ozs7Ozs4QkFFMUQsOERBQUMyQjtvQkFBS2hDLE9BQU87d0JBQUVVLE9BQU87d0JBQVF1QixXQUFXO29CQUFFO29CQUFHQyxVQUFVeEM7O3NDQUN0RCw4REFBQ0s7NEJBQUlDLE9BQU87Z0NBQUVlLGNBQWM7NEJBQUc7OzhDQUM3Qiw4REFBQ29CO29DQUFNbkMsT0FBTzt3Q0FBRWdCLFlBQVk7d0NBQUtFLE9BQU87d0NBQVFELFVBQVU7b0NBQUc7OENBQUc7Ozs7Ozs4Q0FDaEUsOERBQUNtQjtvQ0FDQ0MsTUFBSztvQ0FDTEMsT0FBT2xFO29DQUNQbUUsVUFBVTVDLENBQUFBLElBQUt0QixTQUFTc0IsRUFBRTZDLE1BQU0sQ0FBQ0YsS0FBSztvQ0FDdENHLFFBQVE7b0NBQ1J6QyxPQUFPO3dDQUFFVSxPQUFPO3dDQUFRSixTQUFTO3dDQUFhTyxRQUFRO3dDQUFxQk4sY0FBYzt3Q0FBRzBCLFdBQVc7d0NBQUdoQixVQUFVO29DQUFHO29DQUN2SHlCLGFBQVk7Ozs7Ozs7Ozs7OztzQ0FHaEIsOERBQUMzQzs0QkFBSUMsT0FBTztnQ0FBRWUsY0FBYztnQ0FBRzRCLFVBQVU7NEJBQVc7OzhDQUNsRCw4REFBQ1I7b0NBQU1uQyxPQUFPO3dDQUFFZ0IsWUFBWTt3Q0FBS0UsT0FBTzt3Q0FBUUQsVUFBVTtvQ0FBRzs4Q0FBRzs7Ozs7OzhDQUNoRSw4REFBQ21CO29DQUNDQyxNQUFNN0QsZUFBZSxTQUFTO29DQUM5QjhELE9BQU9oRTtvQ0FDUGlFLFVBQVU1QyxDQUFBQSxJQUFLcEIsWUFBWW9CLEVBQUU2QyxNQUFNLENBQUNGLEtBQUs7b0NBQ3pDRyxRQUFRO29DQUNSekMsT0FBTzt3Q0FBRVUsT0FBTzt3Q0FBUUosU0FBUzt3Q0FBYU8sUUFBUTt3Q0FBcUJOLGNBQWM7d0NBQUcwQixXQUFXO3dDQUFHaEIsVUFBVTtvQ0FBRztvQ0FDdkh5QixhQUFZOzs7Ozs7OENBRWQsOERBQUNYO29DQUFLVCxTQUFTLElBQU03QyxnQkFBZ0JtRSxDQUFBQSxJQUFLLENBQUNBO29DQUFJNUMsT0FBTzt3Q0FBRTJDLFVBQVU7d0NBQVlFLE9BQU87d0NBQUlDLEtBQUs7d0NBQUl0QixRQUFRO3dDQUFXTixPQUFPO29DQUFPOzhDQUNoSTFDLDZCQUFlLDhEQUFDWCwrREFBYUE7Ozs7NkRBQU0sOERBQUNELDZEQUFXQTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHcEQsOERBQUNtQzs0QkFBSUMsT0FBTztnQ0FBRUUsU0FBUztnQ0FBUUMsWUFBWTtnQ0FBVUMsZ0JBQWdCO2dDQUFpQlcsY0FBYzs0QkFBRzs7OENBQ3JHLDhEQUFDb0I7b0NBQU1uQyxPQUFPO3dDQUFFRSxTQUFTO3dDQUFRQyxZQUFZO3dDQUFVYyxVQUFVO3dDQUFJQyxPQUFPO29DQUFPOztzREFDakYsOERBQUNrQjs0Q0FBTUMsTUFBSzs0Q0FBV3JDLE9BQU87Z0RBQUUrQyxhQUFhOzRDQUFFOzs7Ozs7d0NBQUs7Ozs7Ozs7OENBRXRELDhEQUFDQztvQ0FBRTVELE1BQUs7b0NBQUlZLE9BQU87d0NBQUVrQixPQUFPO3dDQUFXRixZQUFZO3dDQUFLQyxVQUFVO3dDQUFJZ0MsZ0JBQWdCO29DQUFPOzhDQUFHOzs7Ozs7Ozs7Ozs7d0JBRWpHdkUsdUJBQVMsOERBQUNxQjs0QkFBSUMsT0FBTztnQ0FBRWtCLE9BQU87Z0NBQVdILGNBQWM7Z0NBQUdFLFVBQVU7NEJBQUc7c0NBQUl2Qzs7Ozs7O3NDQUM1RSw4REFBQzJDOzRCQUFPZ0IsTUFBSzs0QkFBU3JDLE9BQU87Z0NBQUVVLE9BQU87Z0NBQVFKLFNBQVM7Z0NBQVVELFlBQVk7Z0NBQVdhLE9BQU87Z0NBQVNMLFFBQVE7Z0NBQVFOLGNBQWM7Z0NBQUdVLFVBQVU7Z0NBQUlELFlBQVk7Z0NBQUtELGNBQWM7Z0NBQUdTLFFBQVE7NEJBQVU7c0NBQUc7Ozs7Ozs7Ozs7Ozs4QkFJaE4sOERBQUN6QjtvQkFBSUMsT0FBTzt3QkFBRWlDLFdBQVc7d0JBQUdmLE9BQU87d0JBQVFELFVBQVU7b0JBQUc7O3dCQUFHO3NDQUM3Qiw4REFBQytCOzRCQUFFNUQsTUFBSzs0QkFBVVksT0FBTztnQ0FBRWtCLE9BQU87Z0NBQVdGLFlBQVk7Z0NBQUtpQyxnQkFBZ0I7NEJBQU87c0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzlIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL3NyYy9hcHAvbG9naW4vcGFnZS50c3g/ZmM2MyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCB9IGZyb20gXCJAc3VwYWJhc2Uvc3VwYWJhc2UtanNcIjtcbmltcG9ydCB7IEV5ZU9wZW5JY29uLCBFeWVDbG9zZWRJY29uIH0gZnJvbSBcIi4uL2NvbXBvbmVudHMvRXllSWNvbnNcIjtcblxuY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoXG4gIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXG4gIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuKTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9naW5QYWdlKCkge1xuICBjb25zdCBbZW1haWwsIHNldEVtYWlsXSA9IHVzZVN0YXRlKFwiXCIpO1xuICBjb25zdCBbcGFzc3dvcmQsIHNldFBhc3N3b3JkXSA9IHVzZVN0YXRlKFwiXCIpO1xuICBjb25zdCBbc2hvd1Bhc3N3b3JkLCBzZXRTaG93UGFzc3dvcmRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKFwiXCIpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgeyBkYXRhOiBhdXRoTGlzdGVuZXIgfSA9IHN1cGFiYXNlLmF1dGgub25BdXRoU3RhdGVDaGFuZ2UoKGV2ZW50LCBzZXNzaW9uKSA9PiB7XG4gICAgICBpZiAoc2Vzc2lvbikge1xuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IFwiL2Rhc2hib2FyZFwiO1xuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBhdXRoTGlzdGVuZXIuc3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XG4gICAgfTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGhhbmRsZU9BdXRoTG9naW4gPSBhc3luYyAocHJvdmlkZXI6IFwiZ29vZ2xlXCIgfCBcImFwcGxlXCIgfCBcImF6dXJlXCIpID0+IHtcbiAgICBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25JbldpdGhPQXV0aCh7IHByb3ZpZGVyIH0pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUxvZ2luID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRFcnJvcihcIlwiKTtcbiAgICBjb25zdCB7IGVycm9yLCBkYXRhIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25JbldpdGhQYXNzd29yZCh7IGVtYWlsLCBwYXNzd29yZCB9KTtcbiAgICBpZiAoZXJyb3IpIHNldEVycm9yKGVycm9yLm1lc3NhZ2UpO1xuICAgIGVsc2Ugd2luZG93LmxvY2F0aW9uLmhyZWYgPSBcIi9kYXNoYm9hcmRcIjtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgIG1pbkhlaWdodDogXCIxMDB2aFwiLFxuICAgICAgZGlzcGxheTogXCJmbGV4XCIsXG4gICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxuICAgICAganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIsXG4gICAgICBiYWNrZ3JvdW5kOiBcImxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNWY3ZmEgMCUsICNlOGY1ZTkgMTAwJSlcIixcbiAgICAgIHBhZGRpbmc6IDE2LFxuICAgIH19PlxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBiYWNrZ3JvdW5kOiBcIndoaXRlXCIsXG4gICAgICAgIHBhZGRpbmc6IFwiNDhweCAzMnB4XCIsXG4gICAgICAgIGJvcmRlclJhZGl1czogXCIxNnB4XCIsXG4gICAgICAgIGJveFNoYWRvdzogXCIwIDhweCAzMnB4IDAgcmdiYSgzMSwgMzgsIDEzNSwgMC4xMClcIixcbiAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXG4gICAgICAgIGZsZXhEaXJlY3Rpb246IFwiY29sdW1uXCIsXG4gICAgICAgIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsXG4gICAgICAgIHdpZHRoOiBcIjEwMCVcIixcbiAgICAgICAgbWF4V2lkdGg6IDUwMCxcbiAgICAgICAgbWluV2lkdGg6IDAsXG4gICAgICAgIGJvcmRlcjogXCIxLjVweCBzb2xpZCAjMURCRjczXCIsXG4gICAgICB9fT5cbiAgICAgICAgPGgyIHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogOCwgZm9udFdlaWdodDogNzAwLCBmb250U2l6ZTogMjYsIGNvbG9yOiBcIiMxYTNjMzRcIiB9fT5XZWxjb21lIHRvIFdQIEFJPC9oMj5cbiAgICAgICAgPGRpdiBzdHlsZT17eyB3aWR0aDogXCIxMDAlXCIsIG1hcmdpbjogXCIyNHB4IDAgMTJweCAwXCIsIGRpc3BsYXk6IFwiZmxleFwiLCBnYXA6IDggfX0+XG4gICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVPQXV0aExvZ2luKFwiZ29vZ2xlXCIpfVxuICAgICAgICAgICAgc3R5bGU9e3sgZmxleDogMSwgYmFja2dyb3VuZDogXCIjZmZmXCIsIGJvcmRlcjogXCIxcHggc29saWQgI2UwZTBlMFwiLCBib3JkZXJSYWRpdXM6IDgsIHBhZGRpbmc6IDgsIGRpc3BsYXk6IFwiZmxleFwiLCBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLCBqdXN0aWZ5Q29udGVudDogXCJjZW50ZXJcIiwgZ2FwOiA4LCBmb250V2VpZ2h0OiA2MDAsIGN1cnNvcjogXCJwb2ludGVyXCIgfX0+XG4gICAgICAgICAgPHN2ZyB3aWR0aD1cIjE4XCIgaGVpZ2h0PVwiMThcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICA8cGF0aCBmaWxsPVwiIzQyODVGNFwiIGQ9XCJNMjIuNTYgMTIuMjVjMC0uNzgtLjA3LTEuNTMtLjItMi4yNUgxMnY0LjI2aDUuOTJjLS4yNiAxLjM3LTEuMDQgMi41My0yLjIxIDMuMzF2Mi43N2gzLjU3YzIuMDgtMS45MiAzLjI4LTQuNzQgMy4yOC04LjA5elwiLz5cbiAgICAgICAgICAgIDxwYXRoIGZpbGw9XCIjMzRBODUzXCIgZD1cIk0xMiAyM2MyLjk3IDAgNS40Ni0uOTggNy4yOC0yLjY2bC0zLjU3LTIuNzdjLS45OC42Ni0yLjIzIDEuMDYtMy43MSAxLjA2LTIuODYgMC01LjI5LTEuOTMtNi4xNi00LjUzSDIuMTh2Mi44NEMzLjk5IDIwLjUzIDcuNyAyMyAxMiAyM3pcIi8+XG4gICAgICAgICAgICA8cGF0aCBmaWxsPVwiI0ZCQkMwNVwiIGQ9XCJNNS44NCAxNC4wOWMtLjIyLS42Ni0uMzUtMS4zNi0uMzUtMi4wOXMuMTMtMS40My4zNS0yLjA5VjcuMDdIMi4xOEMxLjQzIDguNTUgMSAxMC4yMiAxIDEycy40MyAzLjQ1IDEuMTggNC45M2wyLjg1LTIuMjIuODEtLjYyelwiLz5cbiAgICAgICAgICAgIDxwYXRoIGZpbGw9XCIjRUE0MzM1XCIgZD1cIk0xMiA1LjM4YzEuNjIgMCAzLjA2LjU2IDQuMjEgMS42NGwzLjE1LTMuMTVDMTcuNDUgMi4wOSAxNC45NyAxIDEyIDEgNy43IDEgMy45OSAzLjQ3IDIuMTggNy4wN2wzLjY2IDIuODRjLjg3LTIuNiAzLjMtNC41MyA2LjE2LTQuNTN6XCIvPlxuICAgICAgICAgIDwvc3ZnPiAgICAgICAgICAgIDxzcGFuPkdvb2dsZTwvc3Bhbj5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9eygpID0+IGhhbmRsZU9BdXRoTG9naW4oXCJhcHBsZVwiKX1cbiAgICAgICAgICAgIHN0eWxlPXt7IGZsZXg6IDEsIGJhY2tncm91bmQ6IFwiI2ZmZlwiLCBib3JkZXI6IFwiMXB4IHNvbGlkICNlMGUwZTBcIiwgYm9yZGVyUmFkaXVzOiA4LCBwYWRkaW5nOiA4LCBkaXNwbGF5OiBcImZsZXhcIiwgYWxpZ25JdGVtczogXCJjZW50ZXJcIiwganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIsIGdhcDogOCwgZm9udFdlaWdodDogNjAwLCBjdXJzb3I6IFwicG9pbnRlclwiIH19PlxuICAgICAgICAgIDxzdmcgd2lkdGg9XCIxOFwiIGhlaWdodD1cIjE4XCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggZmlsbD1cIiMwMDBcIiBkPVwiTTE3LjA1IDIwLjI4Yy0uOTguOTUtMi4wNS44LTMuMDguMzUtMS4wOS0uNDYtMi4wOS0uNDgtMy4yNCAwLTEuNDQuNjItMi4yLjQ0LTMuMDYtLjM1QzIuNzkgMTUuMjUgMy41MSA3LjU5IDkuMDUgNy4zMWMxLjM1LjA3IDIuMjkuNzQgMy4wOC44IDEuMTgtLjI0IDIuMzEtLjkzIDMuNTctLjg0IDEuNTEuMTIgMi42NS43MiAzLjQgMS44LTMuMTIgMS44Ny0yLjM4IDUuOTguNDggNy4xMy0uNTcgMS41LTEuMzEgMi45OS0yLjU0IDQuMDlsLjAxLS4wMXpNMTIuMDMgNy4yNWMtLjE1LTIuMjMgMS42Ni00LjA3IDMuNzQtNC4yNS4yOSAyLjU4LTIuMzQgNC41LTMuNzQgNC4yNXpcIi8+XG4gICAgICAgICAgPC9zdmc+ICAgICAgICAgICAgPHNwYW4+QXBwbGU8L3NwYW4+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVPQXV0aExvZ2luKFwiYXp1cmVcIil9XG4gICAgICAgICAgICBzdHlsZT17eyBmbGV4OiAxLCBiYWNrZ3JvdW5kOiBcIiNmZmZcIiwgYm9yZGVyOiBcIjFweCBzb2xpZCAjZTBlMGUwXCIsIGJvcmRlclJhZGl1czogOCwgcGFkZGluZzogOCwgZGlzcGxheTogXCJmbGV4XCIsIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsIGp1c3RpZnlDb250ZW50OiBcImNlbnRlclwiLCBnYXA6IDgsIGZvbnRXZWlnaHQ6IDYwMCwgY3Vyc29yOiBcInBvaW50ZXJcIiB9fT5cbiAgICAgICAgICA8c3ZnIHdpZHRoPVwiMThcIiBoZWlnaHQ9XCIxOFwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgIDxwYXRoIGZpbGw9XCIjRjI1MDIyXCIgZD1cIk0xMS40IDExLjRIMi42VjIuNmg4Ljh2OC44elwiLz5cbiAgICAgICAgICAgIDxwYXRoIGZpbGw9XCIjMDBBNEVGXCIgZD1cIk0yMS40IDExLjRoLTguOFYyLjZoOC44djguOHpcIi8+XG4gICAgICAgICAgICA8cGF0aCBmaWxsPVwiIzdGQkEwMFwiIGQ9XCJNMTEuNCAyMS40SDIuNnYtOC44aDguOHY4Ljh6XCIvPlxuICAgICAgICAgICAgPHBhdGggZmlsbD1cIiNGRkI5MDBcIiBkPVwiTTIxLjQgMjEuNGgtOC44di04LjhoOC44djguOHpcIi8+XG4gICAgICAgICAgPC9zdmc+ICAgICAgICAgICAgPHNwYW4+TWljcm9zb2Z0PC9zcGFuPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBzdHlsZT17eyB3aWR0aDogXCIxMDAlXCIsIGRpc3BsYXk6IFwiZmxleFwiLCBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLCBtYXJnaW46IFwiMTZweCAwIDhweCAwXCIgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBmbGV4OiAxLCBoZWlnaHQ6IDEsIGJhY2tncm91bmQ6IFwiI2UwZTBlMFwiIH19IC8+XG4gICAgICAgICAgPHNwYW4gc3R5bGU9e3sgbWFyZ2luOiBcIjAgMTJweFwiLCBjb2xvcjogXCIjYWFhXCIsIGZvbnRTaXplOiAxNCB9fT5PUjwvc3Bhbj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZsZXg6IDEsIGhlaWdodDogMSwgYmFja2dyb3VuZDogXCIjZTBlMGUwXCIgfX0gLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxmb3JtIHN0eWxlPXt7IHdpZHRoOiBcIjEwMCVcIiwgbWFyZ2luVG9wOiA4IH19IG9uU3VibWl0PXtoYW5kbGVMb2dpbn0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206IDE2IH19PlxuICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGZvbnRXZWlnaHQ6IDUwMCwgY29sb3I6IFwiIzIyMlwiLCBmb250U2l6ZTogMTUgfX0+RW1haWw8L2xhYmVsPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgIHZhbHVlPXtlbWFpbH1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2UgPT4gc2V0RW1haWwoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogXCIxMDAlXCIsIHBhZGRpbmc6IFwiMTBweCAxMnB4XCIsIGJvcmRlcjogXCIxcHggc29saWQgI2UwZTBlMFwiLCBib3JkZXJSYWRpdXM6IDgsIG1hcmdpblRvcDogNiwgZm9udFNpemU6IDE1IH19XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgeW91ciBlbWFpbFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiA4LCBwb3NpdGlvbjogXCJyZWxhdGl2ZVwiIH19PlxuICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGZvbnRXZWlnaHQ6IDUwMCwgY29sb3I6IFwiIzIyMlwiLCBmb250U2l6ZTogMTUgfX0+UGFzc3dvcmQ8L2xhYmVsPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9e3Nob3dQYXNzd29yZCA/IFwidGV4dFwiIDogXCJwYXNzd29yZFwifVxuICAgICAgICAgICAgICB2YWx1ZT17cGFzc3dvcmR9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtlID0+IHNldFBhc3N3b3JkKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IFwiMTAwJVwiLCBwYWRkaW5nOiBcIjEwcHggMTJweFwiLCBib3JkZXI6IFwiMXB4IHNvbGlkICNlMGUwZTBcIiwgYm9yZGVyUmFkaXVzOiA4LCBtYXJnaW5Ub3A6IDYsIGZvbnRTaXplOiAxNSB9fVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgcGFzc3dvcmRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxzcGFuIG9uQ2xpY2s9eygpID0+IHNldFNob3dQYXNzd29yZCh2ID0+ICF2KX0gc3R5bGU9e3sgcG9zaXRpb246IFwiYWJzb2x1dGVcIiwgcmlnaHQ6IDEyLCB0b3A6IDM4LCBjdXJzb3I6IFwicG9pbnRlclwiLCBjb2xvcjogXCIjODg4XCIgfX0+XG4gICAgICAgICAgICAgIHtzaG93UGFzc3dvcmQgPyA8RXllQ2xvc2VkSWNvbiAvPiA6IDxFeWVPcGVuSWNvbiAvPn1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6IFwiZmxleFwiLCBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLCBqdXN0aWZ5Q29udGVudDogXCJzcGFjZS1iZXR3ZWVuXCIsIG1hcmdpbkJvdHRvbTogMTYgfX0+XG4gICAgICAgICAgICA8bGFiZWwgc3R5bGU9e3sgZGlzcGxheTogXCJmbGV4XCIsIGFsaWduSXRlbXM6IFwiY2VudGVyXCIsIGZvbnRTaXplOiAxNCwgY29sb3I6IFwiIzU1NVwiIH19PlxuICAgICAgICAgICAgICA8aW5wdXQgdHlwZT1cImNoZWNrYm94XCIgc3R5bGU9e3sgbWFyZ2luUmlnaHQ6IDYgfX0gLz4gUmVtZW1iZXIgbWVcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8YSBocmVmPVwiI1wiIHN0eWxlPXt7IGNvbG9yOiBcIiMxREJGNzNcIiwgZm9udFdlaWdodDogNTAwLCBmb250U2l6ZTogMTQsIHRleHREZWNvcmF0aW9uOiBcIm5vbmVcIiB9fT5Gb3Jnb3QgUGFzc3dvcmQ/PC9hPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIHtlcnJvciAmJiA8ZGl2IHN0eWxlPXt7IGNvbG9yOiBcIiNlNTM5MzVcIiwgbWFyZ2luQm90dG9tOiA4LCBmb250U2l6ZTogMTQgfX0+e2Vycm9yfTwvZGl2Pn1cbiAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBzdHlsZT17eyB3aWR0aDogXCIxMDAlXCIsIHBhZGRpbmc6IFwiMTJweCAwXCIsIGJhY2tncm91bmQ6IFwiIzFEQkY3M1wiLCBjb2xvcjogXCJ3aGl0ZVwiLCBib3JkZXI6IFwibm9uZVwiLCBib3JkZXJSYWRpdXM6IDgsIGZvbnRTaXplOiAxNiwgZm9udFdlaWdodDogNjAwLCBtYXJnaW5Cb3R0b206IDgsIGN1cnNvcjogXCJwb2ludGVyXCIgfX0+XG4gICAgICAgICAgICBTaWduIEluXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZm9ybT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6IDgsIGNvbG9yOiBcIiM0NDRcIiwgZm9udFNpemU6IDE1IH19PlxuICAgICAgICAgIERvbiZhcG9zO3QgaGF2ZSBhbiBhY2NvdW50PyA8YSBocmVmPVwiL3NpZ251cFwiIHN0eWxlPXt7IGNvbG9yOiBcIiMxREJGNzNcIiwgZm9udFdlaWdodDogNjAwLCB0ZXh0RGVjb3JhdGlvbjogXCJub25lXCIgfX0+U2lnbiBVcDwvYT5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJjcmVhdGVDbGllbnQiLCJFeWVPcGVuSWNvbiIsIkV5ZUNsb3NlZEljb24iLCJzdXBhYmFzZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsIkxvZ2luUGFnZSIsImVtYWlsIiwic2V0RW1haWwiLCJwYXNzd29yZCIsInNldFBhc3N3b3JkIiwic2hvd1Bhc3N3b3JkIiwic2V0U2hvd1Bhc3N3b3JkIiwiZXJyb3IiLCJzZXRFcnJvciIsImRhdGEiLCJhdXRoTGlzdGVuZXIiLCJhdXRoIiwib25BdXRoU3RhdGVDaGFuZ2UiLCJldmVudCIsInNlc3Npb24iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJzdWJzY3JpcHRpb24iLCJ1bnN1YnNjcmliZSIsImhhbmRsZU9BdXRoTG9naW4iLCJwcm92aWRlciIsInNpZ25JbldpdGhPQXV0aCIsImhhbmRsZUxvZ2luIiwiZSIsInByZXZlbnREZWZhdWx0Iiwic2lnbkluV2l0aFBhc3N3b3JkIiwibWVzc2FnZSIsImRpdiIsInN0eWxlIiwibWluSGVpZ2h0IiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImJhY2tncm91bmQiLCJwYWRkaW5nIiwiYm9yZGVyUmFkaXVzIiwiYm94U2hhZG93IiwiZmxleERpcmVjdGlvbiIsIndpZHRoIiwibWF4V2lkdGgiLCJtaW5XaWR0aCIsImJvcmRlciIsImgyIiwibWFyZ2luQm90dG9tIiwiZm9udFdlaWdodCIsImZvbnRTaXplIiwiY29sb3IiLCJtYXJnaW4iLCJnYXAiLCJidXR0b24iLCJvbkNsaWNrIiwiZmxleCIsImN1cnNvciIsInN2ZyIsImhlaWdodCIsInZpZXdCb3giLCJwYXRoIiwiZmlsbCIsImQiLCJzcGFuIiwiZm9ybSIsIm1hcmdpblRvcCIsIm9uU3VibWl0IiwibGFiZWwiLCJpbnB1dCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicmVxdWlyZWQiLCJwbGFjZWhvbGRlciIsInBvc2l0aW9uIiwidiIsInJpZ2h0IiwidG9wIiwibWFyZ2luUmlnaHQiLCJhIiwidGV4dERlY29yYXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch profile from public.profiles\n    const fetchProfile = async (email)=>{\n        const { data, error } = await supabase.from(\"profiles\").select(\"first_name, last_name, email\").eq(\"email\", email).single();\n        if (error) {\n            setProfile(null);\n            setError(error.message);\n        } else {\n            setProfile(data);\n        }\n    };\n    // Update profile in public.profiles\n    const updateProfile = async (updates)=>{\n        if (!profile) return;\n        setLoading(true);\n        const { error } = await supabase.from(\"profiles\").update(updates).eq(\"email\", profile.email);\n        if (error) {\n            setError(error.message);\n        } else {\n            setProfile({\n                ...profile,\n                ...updates\n            });\n        }\n        setLoading(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getSession = async ()=>{\n            setLoading(true);\n            const { data, error } = await supabase.auth.getSession();\n            if (error) {\n                setError(error.message);\n                setSession(null);\n                setUser(null);\n                setProfile(null);\n            } else {\n                setSession(data.session);\n                setUser(data.session?.user ?? null);\n                if (data.session?.user?.email) {\n                    await fetchProfile(data.session.user.email);\n                } else {\n                    setProfile(null);\n                }\n            }\n            setLoading(false);\n        };\n        getSession();\n        const { data: listener } = supabase.auth.onAuthStateChange(async (_event, session)=>{\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user?.email) {\n                await fetchProfile(session.user.email);\n            } else {\n                setProfile(null);\n            }\n        });\n        return ()=>{\n            listener.subscription.unsubscribe();\n        };\n    }, []);\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        setError(null);\n        const { error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) setError(error.message);\n        setLoading(false);\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        setError(null);\n        const { error } = await supabase.auth.signOut();\n        if (error) setError(error.message);\n        setLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            session,\n            profile,\n            setProfile,\n            updateProfile,\n            loading,\n            error,\n            signIn,\n            signOut\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/styles/globals.css":
/*!************************************!*\
  !*** ./src/app/styles/globals.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cd8cab21ee81\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL3N0eWxlcy9nbG9iYWxzLmNzcz8yNTZlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2Q4Y2FiMjFlZTgxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./styles/globals.css */ \"(rsc)/./src/app/styles/globals.css\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/providers/AuthProvider */ \"(rsc)/./src/components/providers/AuthProvider.tsx\");\n\n\n\nconst metadata = {\n    title: \"WordPress AI Builder\",\n    description: \"Automated WordPress site generation with AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Desktop/wp-ai-app/src/app/layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUNzQztBQUU3RCxNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQWlDO0lBQzVFLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFDQyw0RUFBQ1IsNEVBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi9zdHlsZXMvZ2xvYmFscy5jc3MnO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnLi4vY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyJztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1dvcmRQcmVzcyBBSSBCdWlsZGVyJyxcbiAgZGVzY3JpcHRpb246ICdBdXRvbWF0ZWQgV29yZFByZXNzIHNpdGUgZ2VuZXJhdGlvbiB3aXRoIEFJJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/app/login/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Desktop/wp-ai-app/src/components/providers/AuthProvider.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();