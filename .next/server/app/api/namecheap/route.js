/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/namecheap/route";
exports.ids = ["app/api/namecheap/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/namecheap/route.ts */ \"(rsc)/./src/app/api/namecheap/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/namecheap/route\",\n        pathname: \"/api/namecheap\",\n        filename: \"route\",\n        bundlePath: \"app/api/namecheap/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/namecheap/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/namecheap/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/namecheap/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/namecheap/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/app/api/namecheap/route.ts\n\n\nconst COMMON_TLDS = [\n    {\n        tld: \"com\",\n        price: 12.98\n    },\n    {\n        tld: \"net\",\n        price: 14.98\n    },\n    {\n        tld: \"org\",\n        price: 14.98\n    },\n    {\n        tld: \"info\",\n        price: 18.98\n    },\n    {\n        tld: \"biz\",\n        price: 18.98\n    },\n    {\n        tld: \"com.au\",\n        price: 16.50\n    },\n    {\n        tld: \"net.au\",\n        price: 16.50\n    },\n    {\n        tld: \"org.au\",\n        price: 16.50\n    },\n    {\n        tld: \"co\",\n        price: 32.98\n    },\n    {\n        tld: \"io\",\n        price: 59.98\n    }\n];\nconst USD_TO_AUD_RATE = 1.5;\n// ... (checkDomainAvailability, generateDomainVariations, getPriceForDomain functions are unchanged)\nasync function checkDomainAvailability(domainList) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    if (!apiUser || !apiKey || !username || !clientIp) {\n        throw new Error(\"Missing Namecheap API configuration\");\n    }\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.check\",\n        ClientIp: clientIp,\n        DomainList: domainList.join(\",\")\n    });\n    try {\n        const response = await fetch(`${baseUrl}?${params.toString()}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        const xmlText = await response.text();\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n        if (errorMatch) throw new Error(`Namecheap API error: ${errorMatch[1]}`);\n        const domainResults = [];\n        const domainRegex = /<DomainCheckResult[^>]*Domain=\"([^\"]*)\"[^>]*Available=\"([^\"]*)\"[^>]*IsPremiumName=\"([^\"]*)\"[^>]*(?:PremiumRegistrationPrice=\"([^\"]*)\")?[^>]*\\/>/g;\n        let match;\n        while((match = domainRegex.exec(xmlText)) !== null){\n            const [, domainName, available, isPremium, premiumPrice] = match;\n            const result = {\n                Domain: domainName,\n                Available: available === \"true\",\n                IsPremiumName: isPremium === \"true\"\n            };\n            if (result.IsPremiumName && premiumPrice) result.PremiumRegistrationPrice = parseFloat(premiumPrice);\n            domainResults.push(result);\n        }\n        return domainResults;\n    } catch (error) {\n        console.error(\"Namecheap API error:\", error);\n        throw error;\n    }\n}\nfunction generateDomainVariations(baseDomain) {\n    const domainName = baseDomain.replace(/\\.(com|net|org|info|biz|com\\.au|net\\.au|org\\.au|co|io)$/i, \"\");\n    return COMMON_TLDS.map(({ tld })=>`${domainName}.${tld}`);\n}\nfunction getPriceForDomain(domain, namecheapResult) {\n    if (namecheapResult.IsPremiumName && namecheapResult.PremiumRegistrationPrice) {\n        return namecheapResult.PremiumRegistrationPrice * USD_TO_AUD_RATE + 5;\n    }\n    const tld = domain.split(\".\").slice(1).join(\".\");\n    const tldInfo = COMMON_TLDS.find((t)=>t.tld === tld);\n    if (tldInfo) return tldInfo.price * USD_TO_AUD_RATE + 5;\n    return 20 * USD_TO_AUD_RATE + 5;\n}\n// ✨ NEW FUNCTION: To register a domain with Namecheap\nasync function registerDomain(domainName) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    // Registrant contact information sourced from environment variables\n    const registrant = {\n        FirstName: process.env.REGISTRANT_FIRST_NAME,\n        LastName: process.env.REGISTRANT_LAST_NAME,\n        Address1: process.env.REGISTRANT_ADDRESS1,\n        City: process.env.REGISTRANT_CITY,\n        StateProvince: process.env.REGISTRANT_STATE_PROVINCE,\n        PostalCode: process.env.REGISTRANT_POSTAL_CODE,\n        Country: process.env.REGISTRANT_COUNTRY,\n        Phone: process.env.REGISTRANT_PHONE,\n        EmailAddress: process.env.REGISTRANT_EMAIL\n    };\n    for (const [key, value] of Object.entries(registrant)){\n        if (!value) {\n            throw new Error(`Missing required registrant environment variable: REGISTRANT_${key.toUpperCase()}`);\n        }\n    }\n    // ✨ FIX: Correctly map the registrant fields to the required API parameters.\n    // The previous code used `...registrant` which resulted in sending `FirstName` instead of `RegistrantFirstName`.\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.create\",\n        ClientIp: clientIp,\n        DomainName: domainName,\n        Years: \"1\",\n        // Registrant contact details\n        RegistrantFirstName: registrant.FirstName,\n        RegistrantLastName: registrant.LastName,\n        RegistrantAddress1: registrant.Address1,\n        RegistrantCity: registrant.City,\n        RegistrantStateProvince: registrant.StateProvince,\n        RegistrantPostalCode: registrant.PostalCode,\n        RegistrantCountry: registrant.Country,\n        RegistrantPhone: registrant.Phone,\n        RegistrantEmailAddress: registrant.EmailAddress,\n        // Admin, Tech, and AuxBilling contacts (using the same details)\n        AdminFirstName: registrant.FirstName,\n        AdminLastName: registrant.LastName,\n        AdminAddress1: registrant.Address1,\n        AdminCity: registrant.City,\n        AdminStateProvince: registrant.StateProvince,\n        AdminPostalCode: registrant.PostalCode,\n        AdminCountry: registrant.Country,\n        AdminPhone: registrant.Phone,\n        AdminEmailAddress: registrant.EmailAddress,\n        TechFirstName: registrant.FirstName,\n        TechLastName: registrant.LastName,\n        TechAddress1: registrant.Address1,\n        TechCity: registrant.City,\n        TechStateProvince: registrant.StateProvince,\n        TechPostalCode: registrant.PostalCode,\n        TechCountry: registrant.Country,\n        TechPhone: registrant.Phone,\n        TechEmailAddress: registrant.EmailAddress,\n        AuxBillingFirstName: registrant.FirstName,\n        AuxBillingLastName: registrant.LastName,\n        AuxBillingAddress1: registrant.Address1,\n        AuxBillingCity: registrant.City,\n        AuxBillingStateProvince: registrant.StateProvince,\n        AuxBillingPostalCode: registrant.PostalCode,\n        AuxBillingCountry: registrant.Country,\n        AuxBillingPhone: registrant.Phone,\n        AuxBillingEmailAddress: registrant.EmailAddress\n    });\n    try {\n        const response = await fetch(`${baseUrl}?${params.toString()}`, {\n            method: \"POST\"\n        });\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        const xmlText = await response.text();\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n        if (errorMatch) {\n            throw new Error(`Namecheap API Error: ${errorMatch[1]}`);\n        }\n        const successMatch = xmlText.match(/<DomainCreateResult[^>]*Registered=\"true\"[^>]*>/);\n        if (!successMatch) {\n            // Provide a more detailed error if registration is not explicitly confirmed\n            throw new Error(`Domain registration failed. The response from Namecheap did not confirm success. XML: ${xmlText}`);\n        }\n        const orderIdMatch = xmlText.match(/OrderID=\"(\\d+)\"/);\n        const transactionIdMatch = xmlText.match(/TransactionID=\"(\\d+)\"/);\n        return {\n            success: true,\n            message: `Domain ${domainName} registered successfully.`,\n            orderId: orderIdMatch ? orderIdMatch[1] : null,\n            transactionId: transactionIdMatch ? transactionIdMatch[1] : null\n        };\n    } catch (error) {\n        console.error(\"Namecheap registration error:\", error);\n        throw error;\n    }\n}\n// Helper to get Supabase client (service role)\nfunction getSupabaseServiceClient() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n}\n// Function to set CNAME record via Namecheap API\nasync function setCNAMERecord(domain, cnameValue) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    // Namecheap API for DNS setHosts\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.dns.setHosts\",\n        ClientIp: clientIp,\n        SLD: domain.split(\".\")[0],\n        TLD: domain.split(\".\").slice(1).join(\".\"),\n        \"HostName1\": \"@\",\n        \"RecordType1\": \"CNAME\",\n        \"Address1\": cnameValue,\n        \"TTL1\": \"1800\"\n    });\n    const response = await fetch(`${baseUrl}?${params.toString()}`, {\n        method: \"POST\"\n    });\n    const xmlText = await response.text();\n    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n    if (errorMatch) {\n        throw new Error(`Namecheap DNS Error: ${errorMatch[1]}`);\n    }\n    const successMatch = xmlText.match(/<DomainDNSSetHostsResult[^>]*IsSuccess=\"true\"/);\n    if (!successMatch) {\n        throw new Error(`CNAME mapping failed. XML: ${xmlText}`);\n    }\n    return {\n        success: true,\n        message: `CNAME for ${domain} -> ${cnameValue} set successfully.`\n    };\n}\nasync function POST(request) {\n    try {\n        const requestBody = await request.json();\n        const { domain, action, siteId, userId, stripeSessionId } = requestBody;\n        if (!domain) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (action === \"check\") {\n            const domainVariations = generateDomainVariations(domain);\n            const namecheapResults = await checkDomainAvailability(domainVariations);\n            const results = namecheapResults.map((result)=>{\n                const formattedResult = {\n                    Domain: result.Domain,\n                    Available: result.Available,\n                    IsPremiumName: result.IsPremiumName\n                };\n                if (result.Available) {\n                    formattedResult.Price = getPriceForDomain(result.Domain, result);\n                }\n                return formattedResult;\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                results\n            });\n        }\n        if (action === \"register\") {\n            if (!userId) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"userId is required for registration\"\n                }, {\n                    status: 400\n                });\n            }\n            const supabase = getSupabaseServiceClient();\n            // Handle site mapping - if siteId is 'pending', we'll register the domain without site mapping\n            let siteName = null;\n            let finalSiteId = null;\n            if (siteId && siteId !== \"pending\") {\n                // Fetch site_name from Supabase\n                console.log(\"[Namecheap API] Fetching site_name for siteId:\", siteId);\n                const { data: site, error: siteError } = await supabase.from(\"user-websites\").select(\"site_name\").eq(\"id\", siteId).single();\n                if (siteError || !site) {\n                    console.error(\"[Namecheap API] Could not fetch site_name for siteId:\", siteId, siteError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Could not fetch site_name for siteId\"\n                    }, {\n                        status: 400\n                    });\n                }\n                siteName = site.site_name;\n                finalSiteId = siteId;\n                console.log(\"[Namecheap API] site_name fetched:\", siteName);\n            } else {\n                console.log(\"[Namecheap API] Registering domain without site mapping (siteId is pending)\");\n            }\n            // Create or update domain record in database\n            const domainData = {\n                domain_name: domain,\n                user_id: userId,\n                site_id: finalSiteId,\n                status: \"pending\",\n                stripe_session_id: stripeSessionId,\n                cname_target: siteName\n            };\n            // Check if domain record already exists\n            const { data: existingDomain } = await supabase.from(\"domains\").select(\"id\").eq(\"domain_name\", domain).eq(\"user_id\", userId).single();\n            let domainRecord;\n            if (existingDomain) {\n                // Update existing domain record\n                const { data, error: updateError } = await supabase.from(\"domains\").update({\n                    ...domainData,\n                    status: \"pending\"\n                }).eq(\"id\", existingDomain.id).select().single();\n                if (updateError) {\n                    console.error(\"[Namecheap API] Failed to update domain record:\", updateError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Failed to update domain record\"\n                    }, {\n                        status: 500\n                    });\n                }\n                domainRecord = data;\n            } else {\n                // Create new domain record\n                const { data, error: insertError } = await supabase.from(\"domains\").insert(domainData).select().single();\n                if (insertError) {\n                    console.error(\"[Namecheap API] Failed to create domain record:\", insertError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Failed to create domain record\"\n                    }, {\n                        status: 500\n                    });\n                }\n                domainRecord = data;\n            }\n            // Register domain with Namecheap\n            console.log(\"[Namecheap API] Registering domain:\", domain);\n            let registrationResult;\n            try {\n                registrationResult = await registerDomain(domain);\n                console.log(\"[Namecheap API] Registration result:\", registrationResult);\n                // Update domain record with registration details\n                const registrationDate = new Date().toISOString();\n                const expiryDate = new Date();\n                expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year from now\n                await supabase.from(\"domains\").update({\n                    status: \"registered\",\n                    registration_date: registrationDate,\n                    expiry_date: expiryDate.toISOString(),\n                    namecheap_order_id: registrationResult.orderId,\n                    namecheap_transaction_id: registrationResult.transactionId\n                }).eq(\"id\", domainRecord.id);\n            } catch (regError) {\n                console.error(\"[Namecheap API] Registration failed:\", regError);\n                // Update domain record to failed status\n                await supabase.from(\"domains\").update({\n                    status: \"failed\"\n                }).eq(\"id\", domainRecord.id);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: regError instanceof Error ? regError.message : \"Domain registration failed\"\n                }, {\n                    status: 500\n                });\n            }\n            // Set CNAME record and update site mapping only if we have a site\n            let cnameResult = null;\n            let siteNameUpdated = false;\n            if (siteName && finalSiteId) {\n                try {\n                    console.log(\"[Namecheap API] Setting CNAME for domain:\", domain, \"to:\", siteName);\n                    cnameResult = await setCNAMERecord(domain, siteName);\n                    console.log(\"[Namecheap API] CNAME mapping result:\", cnameResult);\n                    // Update domain record with DNS configuration\n                    await supabase.from(\"domains\").update({\n                        dns_configured: true,\n                        status: \"active\"\n                    }).eq(\"id\", domainRecord.id);\n                    // Update site_name to the new domain\n                    console.log(\"[Namecheap API] Updating site_name for siteId:\", finalSiteId, \"to:\", domain);\n                    const { error: updateError } = await supabase.from(\"user-websites\").update({\n                        site_name: domain\n                    }).eq(\"id\", finalSiteId);\n                    if (updateError) {\n                        console.error(\"[Namecheap API] Failed to update site_name:\", updateError);\n                        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                            ...registrationResult,\n                            cnameResult,\n                            updateError: updateError.message\n                        }, {\n                            status: 500\n                        });\n                    }\n                    console.log(\"[Namecheap API] site_name updated successfully.\");\n                    siteNameUpdated = true;\n                } catch (cnameErr) {\n                    console.error(\"[Namecheap API] CNAME mapping error:\", cnameErr);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        ...registrationResult,\n                        cnameError: cnameErr instanceof Error ? cnameErr.message : cnameErr\n                    }, {\n                        status: 500\n                    });\n                }\n            } else {\n                console.log(\"[Namecheap API] Skipping CNAME setup - no site mapping provided\");\n                // Update domain status to registered (not active since no DNS setup)\n                await supabase.from(\"domains\").update({\n                    status: \"registered\"\n                }).eq(\"id\", domainRecord.id);\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                ...registrationResult,\n                cnameResult,\n                siteNameUpdated,\n                domainRecord,\n                message: siteName ? \"Domain registered and mapped to site\" : \"Domain registered successfully (no site mapping)\"\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Invalid action\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error instanceof Error ? error.message : \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9uYW1lY2hlYXAvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsaUNBQWlDO0FBRXVCO0FBQ0g7QUF1QnJELE1BQU1FLGNBQWM7SUFDbEI7UUFBRUMsS0FBSztRQUFPQyxPQUFPO0lBQU07SUFDM0I7UUFBRUQsS0FBSztRQUFPQyxPQUFPO0lBQU07SUFDM0I7UUFBRUQsS0FBSztRQUFPQyxPQUFPO0lBQU07SUFDM0I7UUFBRUQsS0FBSztRQUFRQyxPQUFPO0lBQU07SUFDNUI7UUFBRUQsS0FBSztRQUFPQyxPQUFPO0lBQU07SUFDM0I7UUFBRUQsS0FBSztRQUFVQyxPQUFPO0lBQU07SUFDOUI7UUFBRUQsS0FBSztRQUFVQyxPQUFPO0lBQU07SUFDOUI7UUFBRUQsS0FBSztRQUFVQyxPQUFPO0lBQU07SUFDOUI7UUFBRUQsS0FBSztRQUFNQyxPQUFPO0lBQU07SUFDMUI7UUFBRUQsS0FBSztRQUFNQyxPQUFPO0lBQU07Q0FDM0I7QUFFRCxNQUFNQyxrQkFBa0I7QUFFeEIscUdBQXFHO0FBQ3JHLGVBQWVDLHdCQUF3QkMsVUFBb0I7SUFDekQsTUFBTUMsVUFBVUMsUUFBUUMsR0FBRyxDQUFDQyxrQkFBa0I7SUFDOUMsTUFBTUMsU0FBU0gsUUFBUUMsR0FBRyxDQUFDRyxpQkFBaUI7SUFDNUMsTUFBTUMsV0FBV0wsUUFBUUMsR0FBRyxDQUFDSyxrQkFBa0I7SUFDL0MsTUFBTUMsV0FBV1AsUUFBUUMsR0FBRyxDQUFDTyxtQkFBbUI7SUFDaEQsTUFBTUMsVUFBVVQsUUFBUUMsR0FBRyxDQUFDUyxpQkFBaUIsS0FBSztJQUVsRCxJQUFJLENBQUNYLFdBQVcsQ0FBQ0ksVUFBVSxDQUFDRSxZQUFZLENBQUNFLFVBQVU7UUFDakQsTUFBTSxJQUFJSSxNQUFNO0lBQ2xCO0lBRUEsTUFBTUMsVUFBVUgsVUFDWixtREFDQTtJQUVKLE1BQU1JLFNBQVMsSUFBSUMsZ0JBQWdCO1FBQ2pDQyxTQUFTaEI7UUFDVGlCLFFBQVFiO1FBQ1JjLFVBQVVaO1FBQ1ZhLFNBQVM7UUFDVEMsVUFBVVo7UUFDVmEsWUFBWXRCLFdBQVd1QixJQUFJLENBQUM7SUFDOUI7SUFFQSxJQUFJO1FBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRVgsUUFBUSxDQUFDLEVBQUVDLE9BQU9XLFFBQVEsR0FBRyxDQUFDLEVBQUU7WUFDOURDLFFBQVE7WUFDUkMsU0FBUztnQkFBRSxnQkFBZ0I7WUFBb0M7UUFDakU7UUFFQSxJQUFJLENBQUNKLFNBQVNLLEVBQUUsRUFBRSxNQUFNLElBQUloQixNQUFNLENBQUMsb0JBQW9CLEVBQUVXLFNBQVNNLE1BQU0sQ0FBQyxDQUFDO1FBQzFFLE1BQU1DLFVBQVUsTUFBTVAsU0FBU1EsSUFBSTtRQUNuQyxNQUFNQyxhQUFhRixRQUFRRyxLQUFLLENBQUM7UUFDakMsSUFBSUQsWUFBWSxNQUFNLElBQUlwQixNQUFNLENBQUMscUJBQXFCLEVBQUVvQixVQUFVLENBQUMsRUFBRSxDQUFDLENBQUM7UUFFdkUsTUFBTUUsZ0JBQXlDLEVBQUU7UUFDakQsTUFBTUMsY0FBYztRQUNwQixJQUFJRjtRQUNKLE1BQU8sQ0FBQ0EsUUFBUUUsWUFBWUMsSUFBSSxDQUFDTixRQUFPLE1BQU8sS0FBTTtZQUNuRCxNQUFNLEdBQUdPLFlBQVlDLFdBQVdDLFdBQVdDLGFBQWEsR0FBR1A7WUFDM0QsTUFBTVEsU0FBZ0M7Z0JBQ3BDQyxRQUFRTDtnQkFDUk0sV0FBV0wsY0FBYztnQkFDekJNLGVBQWVMLGNBQWM7WUFDL0I7WUFDQSxJQUFJRSxPQUFPRyxhQUFhLElBQUlKLGNBQWNDLE9BQU9JLHdCQUF3QixHQUFHQyxXQUFXTjtZQUN2Rk4sY0FBY2EsSUFBSSxDQUFDTjtRQUNyQjtRQUNBLE9BQU9QO0lBQ1QsRUFBRSxPQUFPYyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3RDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBLFNBQVNFLHlCQUF5QkMsVUFBa0I7SUFDbEQsTUFBTWQsYUFBYWMsV0FBV0MsT0FBTyxDQUFDLDREQUE0RDtJQUNsRyxPQUFPMUQsWUFBWTJELEdBQUcsQ0FBQyxDQUFDLEVBQUUxRCxHQUFHLEVBQUUsR0FBSyxDQUFDLEVBQUUwQyxXQUFXLENBQUMsRUFBRTFDLElBQUksQ0FBQztBQUM1RDtBQUVBLFNBQVMyRCxrQkFBa0JDLE1BQWMsRUFBRUMsZUFBc0M7SUFDL0UsSUFBSUEsZ0JBQWdCWixhQUFhLElBQUlZLGdCQUFnQlgsd0JBQXdCLEVBQUU7UUFDN0UsT0FBT1csZ0JBQWdCWCx3QkFBd0IsR0FBR2hELGtCQUFrQjtJQUN0RTtJQUNBLE1BQU1GLE1BQU00RCxPQUFPRSxLQUFLLENBQUMsS0FBS0MsS0FBSyxDQUFDLEdBQUdwQyxJQUFJLENBQUM7SUFDNUMsTUFBTXFDLFVBQVVqRSxZQUFZa0UsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFbEUsR0FBRyxLQUFLQTtJQUNoRCxJQUFJZ0UsU0FBUyxPQUFPQSxRQUFRL0QsS0FBSyxHQUFHQyxrQkFBa0I7SUFDdEQsT0FBTyxLQUFLQSxrQkFBa0I7QUFDaEM7QUFHQSxzREFBc0Q7QUFDdEQsZUFBZWlFLGVBQWV6QixVQUFrQjtJQUM5QyxNQUFNckMsVUFBVUMsUUFBUUMsR0FBRyxDQUFDQyxrQkFBa0I7SUFDOUMsTUFBTUMsU0FBU0gsUUFBUUMsR0FBRyxDQUFDRyxpQkFBaUI7SUFDNUMsTUFBTUMsV0FBV0wsUUFBUUMsR0FBRyxDQUFDSyxrQkFBa0I7SUFDL0MsTUFBTUMsV0FBV1AsUUFBUUMsR0FBRyxDQUFDTyxtQkFBbUI7SUFDaEQsTUFBTUMsVUFBVVQsUUFBUUMsR0FBRyxDQUFDUyxpQkFBaUIsS0FBSztJQUVsRCxNQUFNRSxVQUFVSCxVQUNaLG1EQUNBO0lBRUosb0VBQW9FO0lBQ3BFLE1BQU1xRCxhQUFhO1FBQ2pCQyxXQUFXL0QsUUFBUUMsR0FBRyxDQUFDK0QscUJBQXFCO1FBQzVDQyxVQUFVakUsUUFBUUMsR0FBRyxDQUFDaUUsb0JBQW9CO1FBQzFDQyxVQUFVbkUsUUFBUUMsR0FBRyxDQUFDbUUsbUJBQW1CO1FBQ3pDQyxNQUFNckUsUUFBUUMsR0FBRyxDQUFDcUUsZUFBZTtRQUNqQ0MsZUFBZXZFLFFBQVFDLEdBQUcsQ0FBQ3VFLHlCQUF5QjtRQUNwREMsWUFBWXpFLFFBQVFDLEdBQUcsQ0FBQ3lFLHNCQUFzQjtRQUM5Q0MsU0FBUzNFLFFBQVFDLEdBQUcsQ0FBQzJFLGtCQUFrQjtRQUN2Q0MsT0FBTzdFLFFBQVFDLEdBQUcsQ0FBQzZFLGdCQUFnQjtRQUNuQ0MsY0FBYy9FLFFBQVFDLEdBQUcsQ0FBQytFLGdCQUFnQjtJQUM1QztJQUVBLEtBQUssTUFBTSxDQUFDQyxLQUFLQyxNQUFNLElBQUlDLE9BQU9DLE9BQU8sQ0FBQ3RCLFlBQWE7UUFDckQsSUFBSSxDQUFDb0IsT0FBTztZQUNWLE1BQU0sSUFBSXZFLE1BQU0sQ0FBQyw2REFBNkQsRUFBRXNFLElBQUlJLFdBQVcsR0FBRyxDQUFDO1FBQ3JHO0lBQ0Y7SUFFQSw2RUFBNkU7SUFDN0UsaUhBQWlIO0lBQ2pILE1BQU14RSxTQUFTLElBQUlDLGdCQUFnQjtRQUNqQ0MsU0FBU2hCO1FBQ1RpQixRQUFRYjtRQUNSYyxVQUFVWjtRQUNWYSxTQUFTO1FBQ1RDLFVBQVVaO1FBQ1YrRSxZQUFZbEQ7UUFDWm1ELE9BQU87UUFDUCw2QkFBNkI7UUFDN0JDLHFCQUFxQjFCLFdBQVdDLFNBQVM7UUFDekMwQixvQkFBb0IzQixXQUFXRyxRQUFRO1FBQ3ZDeUIsb0JBQW9CNUIsV0FBV0ssUUFBUTtRQUN2Q3dCLGdCQUFnQjdCLFdBQVdPLElBQUk7UUFDL0J1Qix5QkFBeUI5QixXQUFXUyxhQUFhO1FBQ2pEc0Isc0JBQXNCL0IsV0FBV1csVUFBVTtRQUMzQ3FCLG1CQUFtQmhDLFdBQVdhLE9BQU87UUFDckNvQixpQkFBaUJqQyxXQUFXZSxLQUFLO1FBQ2pDbUIsd0JBQXdCbEMsV0FBV2lCLFlBQVk7UUFDL0MsZ0VBQWdFO1FBQ2hFa0IsZ0JBQWdCbkMsV0FBV0MsU0FBUztRQUNwQ21DLGVBQWVwQyxXQUFXRyxRQUFRO1FBQ2xDa0MsZUFBZXJDLFdBQVdLLFFBQVE7UUFDbENpQyxXQUFXdEMsV0FBV08sSUFBSTtRQUMxQmdDLG9CQUFvQnZDLFdBQVdTLGFBQWE7UUFDNUMrQixpQkFBaUJ4QyxXQUFXVyxVQUFVO1FBQ3RDOEIsY0FBY3pDLFdBQVdhLE9BQU87UUFDaEM2QixZQUFZMUMsV0FBV2UsS0FBSztRQUM1QjRCLG1CQUFtQjNDLFdBQVdpQixZQUFZO1FBQzFDMkIsZUFBZTVDLFdBQVdDLFNBQVM7UUFDbkM0QyxjQUFjN0MsV0FBV0csUUFBUTtRQUNqQzJDLGNBQWM5QyxXQUFXSyxRQUFRO1FBQ2pDMEMsVUFBVS9DLFdBQVdPLElBQUk7UUFDekJ5QyxtQkFBbUJoRCxXQUFXUyxhQUFhO1FBQzNDd0MsZ0JBQWdCakQsV0FBV1csVUFBVTtRQUNyQ3VDLGFBQWFsRCxXQUFXYSxPQUFPO1FBQy9Cc0MsV0FBV25ELFdBQVdlLEtBQUs7UUFDM0JxQyxrQkFBa0JwRCxXQUFXaUIsWUFBWTtRQUN6Q29DLHFCQUFxQnJELFdBQVdDLFNBQVM7UUFDekNxRCxvQkFBb0J0RCxXQUFXRyxRQUFRO1FBQ3ZDb0Qsb0JBQW9CdkQsV0FBV0ssUUFBUTtRQUN2Q21ELGdCQUFnQnhELFdBQVdPLElBQUk7UUFDL0JrRCx5QkFBeUJ6RCxXQUFXUyxhQUFhO1FBQ2pEaUQsc0JBQXNCMUQsV0FBV1csVUFBVTtRQUMzQ2dELG1CQUFtQjNELFdBQVdhLE9BQU87UUFDckMrQyxpQkFBaUI1RCxXQUFXZSxLQUFLO1FBQ2pDOEMsd0JBQXdCN0QsV0FBV2lCLFlBQVk7SUFDakQ7SUFFQSxJQUFJO1FBQ0YsTUFBTXpELFdBQVcsTUFBTUMsTUFBTSxDQUFDLEVBQUVYLFFBQVEsQ0FBQyxFQUFFQyxPQUFPVyxRQUFRLEdBQUcsQ0FBQyxFQUFFO1lBQUVDLFFBQVE7UUFBTztRQUNqRixJQUFJLENBQUNILFNBQVNLLEVBQUUsRUFBRSxNQUFNLElBQUloQixNQUFNLENBQUMsb0JBQW9CLEVBQUVXLFNBQVNNLE1BQU0sQ0FBQyxDQUFDO1FBRTFFLE1BQU1DLFVBQVUsTUFBTVAsU0FBU1EsSUFBSTtRQUVuQyxNQUFNQyxhQUFhRixRQUFRRyxLQUFLLENBQUM7UUFDakMsSUFBSUQsWUFBWTtZQUNkLE1BQU0sSUFBSXBCLE1BQU0sQ0FBQyxxQkFBcUIsRUFBRW9CLFVBQVUsQ0FBQyxFQUFFLENBQUMsQ0FBQztRQUN6RDtRQUVBLE1BQU02RixlQUFlL0YsUUFBUUcsS0FBSyxDQUFDO1FBQ25DLElBQUksQ0FBQzRGLGNBQWM7WUFDakIsNEVBQTRFO1lBQzVFLE1BQU0sSUFBSWpILE1BQU0sQ0FBQyxzRkFBc0YsRUFBRWtCLFFBQVEsQ0FBQztRQUNwSDtRQUVBLE1BQU1nRyxlQUFlaEcsUUFBUUcsS0FBSyxDQUFDO1FBQ25DLE1BQU04RixxQkFBcUJqRyxRQUFRRyxLQUFLLENBQUM7UUFFekMsT0FBTztZQUNMK0YsU0FBUztZQUNUQyxTQUFTLENBQUMsT0FBTyxFQUFFNUYsV0FBVyx5QkFBeUIsQ0FBQztZQUN4RDZGLFNBQVNKLGVBQWVBLFlBQVksQ0FBQyxFQUFFLEdBQUc7WUFDMUNLLGVBQWVKLHFCQUFxQkEsa0JBQWtCLENBQUMsRUFBRSxHQUFHO1FBQzlEO0lBQ0YsRUFBRSxPQUFPL0UsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtRQUMvQyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSwrQ0FBK0M7QUFDL0MsU0FBU29GO0lBQ1AsT0FBTzNJLG1FQUFZQSxDQUNqQlEsMENBQW9DLEVBQ3BDQSxRQUFRQyxHQUFHLENBQUNvSSx5QkFBeUI7QUFFekM7QUFFQSxpREFBaUQ7QUFDakQsZUFBZUMsZUFBZWhGLE1BQWMsRUFBRWlGLFVBQWtCO0lBQzlELE1BQU14SSxVQUFVQyxRQUFRQyxHQUFHLENBQUNDLGtCQUFrQjtJQUM5QyxNQUFNQyxTQUFTSCxRQUFRQyxHQUFHLENBQUNHLGlCQUFpQjtJQUM1QyxNQUFNQyxXQUFXTCxRQUFRQyxHQUFHLENBQUNLLGtCQUFrQjtJQUMvQyxNQUFNQyxXQUFXUCxRQUFRQyxHQUFHLENBQUNPLG1CQUFtQjtJQUNoRCxNQUFNQyxVQUFVVCxRQUFRQyxHQUFHLENBQUNTLGlCQUFpQixLQUFLO0lBQ2xELE1BQU1FLFVBQVVILFVBQ1osbURBQ0E7SUFFSixpQ0FBaUM7SUFDakMsTUFBTUksU0FBUyxJQUFJQyxnQkFBZ0I7UUFDakNDLFNBQVNoQjtRQUNUaUIsUUFBUWI7UUFDUmMsVUFBVVo7UUFDVmEsU0FBUztRQUNUQyxVQUFVWjtRQUNWaUksS0FBS2xGLE9BQU9FLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUN6QmlGLEtBQUtuRixPQUFPRSxLQUFLLENBQUMsS0FBS0MsS0FBSyxDQUFDLEdBQUdwQyxJQUFJLENBQUM7UUFDckMsYUFBYTtRQUNiLGVBQWU7UUFDZixZQUFZa0g7UUFDWixRQUFRO0lBQ1Y7SUFFQSxNQUFNakgsV0FBVyxNQUFNQyxNQUFNLENBQUMsRUFBRVgsUUFBUSxDQUFDLEVBQUVDLE9BQU9XLFFBQVEsR0FBRyxDQUFDLEVBQUU7UUFBRUMsUUFBUTtJQUFPO0lBQ2pGLE1BQU1JLFVBQVUsTUFBTVAsU0FBU1EsSUFBSTtJQUNuQyxNQUFNQyxhQUFhRixRQUFRRyxLQUFLLENBQUM7SUFDakMsSUFBSUQsWUFBWTtRQUNkLE1BQU0sSUFBSXBCLE1BQU0sQ0FBQyxxQkFBcUIsRUFBRW9CLFVBQVUsQ0FBQyxFQUFFLENBQUMsQ0FBQztJQUN6RDtJQUNBLE1BQU02RixlQUFlL0YsUUFBUUcsS0FBSyxDQUFDO0lBQ25DLElBQUksQ0FBQzRGLGNBQWM7UUFDakIsTUFBTSxJQUFJakgsTUFBTSxDQUFDLDJCQUEyQixFQUFFa0IsUUFBUSxDQUFDO0lBQ3pEO0lBQ0EsT0FBTztRQUFFa0csU0FBUztRQUFNQyxTQUFTLENBQUMsVUFBVSxFQUFFMUUsT0FBTyxJQUFJLEVBQUVpRixXQUFXLGtCQUFrQixDQUFDO0lBQUM7QUFDNUY7QUFFTyxlQUFlRyxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTUMsY0FBYyxNQUFNRCxRQUFRRSxJQUFJO1FBQ3RDLE1BQU0sRUFBRXZGLE1BQU0sRUFBRXdGLE1BQU0sRUFBRUMsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLGVBQWUsRUFBRSxHQUFHTDtRQUU1RCxJQUFJLENBQUN0RixRQUFRO1lBQ1gsT0FBTy9ELGtGQUFZQSxDQUFDc0osSUFBSSxDQUFDO2dCQUFFOUYsT0FBTztZQUFxQixHQUFHO2dCQUFFbkIsUUFBUTtZQUFJO1FBQzFFO1FBRUEsSUFBSWtILFdBQVcsU0FBUztZQUN0QixNQUFNSSxtQkFBbUJqRyx5QkFBeUJLO1lBQ2xELE1BQU02RixtQkFBbUIsTUFBTXRKLHdCQUF3QnFKO1lBQ3ZELE1BQU1FLFVBQStCRCxpQkFBaUIvRixHQUFHLENBQUNaLENBQUFBO2dCQUN4RCxNQUFNNkcsa0JBQXFDO29CQUN6QzVHLFFBQVFELE9BQU9DLE1BQU07b0JBQ3JCQyxXQUFXRixPQUFPRSxTQUFTO29CQUMzQkMsZUFBZUgsT0FBT0csYUFBYTtnQkFDckM7Z0JBQ0EsSUFBSUgsT0FBT0UsU0FBUyxFQUFFO29CQUNwQjJHLGdCQUFnQkMsS0FBSyxHQUFHakcsa0JBQWtCYixPQUFPQyxNQUFNLEVBQUVEO2dCQUMzRDtnQkFDQSxPQUFPNkc7WUFDVDtZQUNBLE9BQU85SixrRkFBWUEsQ0FBQ3NKLElBQUksQ0FBQztnQkFBRU87WUFBUTtRQUNyQztRQUVBLElBQUlOLFdBQVcsWUFBWTtZQUN6QixJQUFJLENBQUNFLFFBQVE7Z0JBQ1gsT0FBT3pKLGtGQUFZQSxDQUFDc0osSUFBSSxDQUFDO29CQUFFOUYsT0FBTztnQkFBc0MsR0FBRztvQkFBRW5CLFFBQVE7Z0JBQUk7WUFDM0Y7WUFFQSxNQUFNMkgsV0FBV3BCO1lBRWpCLCtGQUErRjtZQUMvRixJQUFJcUIsV0FBVztZQUNmLElBQUlDLGNBQWM7WUFFbEIsSUFBSVYsVUFBVUEsV0FBVyxXQUFXO2dCQUNsQyxnQ0FBZ0M7Z0JBQ2hDL0YsUUFBUTBHLEdBQUcsQ0FBQyxrREFBa0RYO2dCQUM5RCxNQUFNLEVBQUVZLE1BQU1DLElBQUksRUFBRTdHLE9BQU84RyxTQUFTLEVBQUUsR0FBRyxNQUFNTixTQUM1Q08sSUFBSSxDQUFDLGlCQUNMQyxNQUFNLENBQUMsYUFDUEMsRUFBRSxDQUFDLE1BQU1qQixRQUNUa0IsTUFBTTtnQkFDVCxJQUFJSixhQUFhLENBQUNELE1BQU07b0JBQ3RCNUcsUUFBUUQsS0FBSyxDQUFDLHlEQUF5RGdHLFFBQVFjO29CQUMvRSxPQUFPdEssa0ZBQVlBLENBQUNzSixJQUFJLENBQUM7d0JBQUU5RixPQUFPO29CQUF1QyxHQUFHO3dCQUFFbkIsUUFBUTtvQkFBSTtnQkFDNUY7Z0JBQ0E0SCxXQUFXSSxLQUFLTSxTQUFTO2dCQUN6QlQsY0FBY1Y7Z0JBQ2QvRixRQUFRMEcsR0FBRyxDQUFDLHNDQUFzQ0Y7WUFDcEQsT0FBTztnQkFDTHhHLFFBQVEwRyxHQUFHLENBQUM7WUFDZDtZQUVBLDZDQUE2QztZQUM3QyxNQUFNUyxhQUFhO2dCQUNqQkMsYUFBYTlHO2dCQUNiK0csU0FBU3JCO2dCQUNUc0IsU0FBU2I7Z0JBQ1Q3SCxRQUFRO2dCQUNSMkksbUJBQW1CdEI7Z0JBQ25CdUIsY0FBY2hCO1lBQ2hCO1lBRUEsd0NBQXdDO1lBQ3hDLE1BQU0sRUFBRUcsTUFBTWMsY0FBYyxFQUFFLEdBQUcsTUFBTWxCLFNBQ3BDTyxJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDLE1BQ1BDLEVBQUUsQ0FBQyxlQUFlMUcsUUFDbEIwRyxFQUFFLENBQUMsV0FBV2hCLFFBQ2RpQixNQUFNO1lBRVQsSUFBSVM7WUFDSixJQUFJRCxnQkFBZ0I7Z0JBQ2xCLGdDQUFnQztnQkFDaEMsTUFBTSxFQUFFZCxJQUFJLEVBQUU1RyxPQUFPNEgsV0FBVyxFQUFFLEdBQUcsTUFBTXBCLFNBQ3hDTyxJQUFJLENBQUMsV0FDTGMsTUFBTSxDQUFDO29CQUFFLEdBQUdULFVBQVU7b0JBQUV2SSxRQUFRO2dCQUFVLEdBQzFDb0ksRUFBRSxDQUFDLE1BQU1TLGVBQWVJLEVBQUUsRUFDMUJkLE1BQU0sR0FDTkUsTUFBTTtnQkFFVCxJQUFJVSxhQUFhO29CQUNmM0gsUUFBUUQsS0FBSyxDQUFDLG1EQUFtRDRIO29CQUNqRSxPQUFPcEwsa0ZBQVlBLENBQUNzSixJQUFJLENBQUM7d0JBQUU5RixPQUFPO29CQUFpQyxHQUFHO3dCQUFFbkIsUUFBUTtvQkFBSTtnQkFDdEY7Z0JBQ0E4SSxlQUFlZjtZQUNqQixPQUFPO2dCQUNMLDJCQUEyQjtnQkFDM0IsTUFBTSxFQUFFQSxJQUFJLEVBQUU1RyxPQUFPK0gsV0FBVyxFQUFFLEdBQUcsTUFBTXZCLFNBQ3hDTyxJQUFJLENBQUMsV0FDTGlCLE1BQU0sQ0FBQ1osWUFDUEosTUFBTSxHQUNORSxNQUFNO2dCQUVULElBQUlhLGFBQWE7b0JBQ2Y5SCxRQUFRRCxLQUFLLENBQUMsbURBQW1EK0g7b0JBQ2pFLE9BQU92TCxrRkFBWUEsQ0FBQ3NKLElBQUksQ0FBQzt3QkFBRTlGLE9BQU87b0JBQWlDLEdBQUc7d0JBQUVuQixRQUFRO29CQUFJO2dCQUN0RjtnQkFDQThJLGVBQWVmO1lBQ2pCO1lBRUEsaUNBQWlDO1lBQ2pDM0csUUFBUTBHLEdBQUcsQ0FBQyx1Q0FBdUNwRztZQUNuRCxJQUFJMEg7WUFDSixJQUFJO2dCQUNGQSxxQkFBcUIsTUFBTW5ILGVBQWVQO2dCQUMxQ04sUUFBUTBHLEdBQUcsQ0FBQyx3Q0FBd0NzQjtnQkFFcEQsaURBQWlEO2dCQUNqRCxNQUFNQyxtQkFBbUIsSUFBSUMsT0FBT0MsV0FBVztnQkFDL0MsTUFBTUMsYUFBYSxJQUFJRjtnQkFDdkJFLFdBQVdDLFdBQVcsQ0FBQ0QsV0FBV0UsV0FBVyxLQUFLLElBQUksa0JBQWtCO2dCQUV4RSxNQUFNL0IsU0FDSE8sSUFBSSxDQUFDLFdBQ0xjLE1BQU0sQ0FBQztvQkFDTmhKLFFBQVE7b0JBQ1IySixtQkFBbUJOO29CQUNuQk8sYUFBYUosV0FBV0QsV0FBVztvQkFDbkNNLG9CQUFvQlQsbUJBQW1CL0MsT0FBTztvQkFDOUN5RCwwQkFBMEJWLG1CQUFtQjlDLGFBQWE7Z0JBQzVELEdBQ0M4QixFQUFFLENBQUMsTUFBTVUsYUFBYUcsRUFBRTtZQUU3QixFQUFFLE9BQU9jLFVBQVU7Z0JBQ2pCM0ksUUFBUUQsS0FBSyxDQUFDLHdDQUF3QzRJO2dCQUV0RCx3Q0FBd0M7Z0JBQ3hDLE1BQU1wQyxTQUNITyxJQUFJLENBQUMsV0FDTGMsTUFBTSxDQUFDO29CQUFFaEosUUFBUTtnQkFBUyxHQUMxQm9JLEVBQUUsQ0FBQyxNQUFNVSxhQUFhRyxFQUFFO2dCQUUzQixPQUFPdEwsa0ZBQVlBLENBQUNzSixJQUFJLENBQUM7b0JBQ3ZCOUYsT0FBTzRJLG9CQUFvQmhMLFFBQVFnTCxTQUFTM0QsT0FBTyxHQUFHO2dCQUN4RCxHQUFHO29CQUFFcEcsUUFBUTtnQkFBSTtZQUNuQjtZQUVBLGtFQUFrRTtZQUNsRSxJQUFJZ0ssY0FBYztZQUNsQixJQUFJQyxrQkFBa0I7WUFFdEIsSUFBSXJDLFlBQVlDLGFBQWE7Z0JBQzNCLElBQUk7b0JBQ0Z6RyxRQUFRMEcsR0FBRyxDQUFDLDZDQUE2Q3BHLFFBQVEsT0FBT2tHO29CQUN4RW9DLGNBQWMsTUFBTXRELGVBQWVoRixRQUFRa0c7b0JBQzNDeEcsUUFBUTBHLEdBQUcsQ0FBQyx5Q0FBeUNrQztvQkFFckQsOENBQThDO29CQUM5QyxNQUFNckMsU0FDSE8sSUFBSSxDQUFDLFdBQ0xjLE1BQU0sQ0FBQzt3QkFDTmtCLGdCQUFnQjt3QkFDaEJsSyxRQUFRO29CQUNWLEdBQ0NvSSxFQUFFLENBQUMsTUFBTVUsYUFBYUcsRUFBRTtvQkFFM0IscUNBQXFDO29CQUNyQzdILFFBQVEwRyxHQUFHLENBQUMsa0RBQWtERCxhQUFhLE9BQU9uRztvQkFDbEYsTUFBTSxFQUFFUCxPQUFPNEgsV0FBVyxFQUFFLEdBQUcsTUFBTXBCLFNBQ2xDTyxJQUFJLENBQUMsaUJBQ0xjLE1BQU0sQ0FBQzt3QkFBRVYsV0FBVzVHO29CQUFPLEdBQzNCMEcsRUFBRSxDQUFDLE1BQU1QO29CQUNaLElBQUlrQixhQUFhO3dCQUNmM0gsUUFBUUQsS0FBSyxDQUFDLCtDQUErQzRIO3dCQUM3RCxPQUFPcEwsa0ZBQVlBLENBQUNzSixJQUFJLENBQUM7NEJBQ3ZCLEdBQUdtQyxrQkFBa0I7NEJBQ3JCWTs0QkFDQWpCLGFBQWFBLFlBQVkzQyxPQUFPO3dCQUNsQyxHQUFHOzRCQUFFcEcsUUFBUTt3QkFBSTtvQkFDbkI7b0JBQ0FvQixRQUFRMEcsR0FBRyxDQUFDO29CQUNabUMsa0JBQWtCO2dCQUVwQixFQUFFLE9BQU9FLFVBQVU7b0JBQ2pCL0ksUUFBUUQsS0FBSyxDQUFDLHdDQUF3Q2dKO29CQUN0RCxPQUFPeE0sa0ZBQVlBLENBQUNzSixJQUFJLENBQUM7d0JBQ3ZCLEdBQUdtQyxrQkFBa0I7d0JBQ3JCZ0IsWUFBWUQsb0JBQW9CcEwsUUFBUW9MLFNBQVMvRCxPQUFPLEdBQUcrRDtvQkFDN0QsR0FBRzt3QkFBRW5LLFFBQVE7b0JBQUk7Z0JBQ25CO1lBQ0YsT0FBTztnQkFDTG9CLFFBQVEwRyxHQUFHLENBQUM7Z0JBQ1oscUVBQXFFO2dCQUNyRSxNQUFNSCxTQUNITyxJQUFJLENBQUMsV0FDTGMsTUFBTSxDQUFDO29CQUFFaEosUUFBUTtnQkFBYSxHQUM5Qm9JLEVBQUUsQ0FBQyxNQUFNVSxhQUFhRyxFQUFFO1lBQzdCO1lBRUEsT0FBT3RMLGtGQUFZQSxDQUFDc0osSUFBSSxDQUFDO2dCQUN2QixHQUFHbUMsa0JBQWtCO2dCQUNyQlk7Z0JBQ0FDO2dCQUNBbkI7Z0JBQ0ExQyxTQUFTd0IsV0FBVyx5Q0FBeUM7WUFDL0Q7UUFDRjtRQUVBLE9BQU9qSyxrRkFBWUEsQ0FBQ3NKLElBQUksQ0FBQztZQUFFOUYsT0FBTztRQUFpQixHQUFHO1lBQUVuQixRQUFRO1FBQUk7SUFDdEUsRUFBRSxPQUFPbUIsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsY0FBY0E7UUFDNUIsT0FBT3hELGtGQUFZQSxDQUFDc0osSUFBSSxDQUN0QjtZQUFFOUYsT0FBT0EsaUJBQWlCcEMsUUFBUW9DLE1BQU1pRixPQUFPLEdBQUc7UUFBd0IsR0FDMUU7WUFBRXBHLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vd29yZHByZXNzLWFpLWFwcC8uL3NyYy9hcHAvYXBpL25hbWVjaGVhcC9yb3V0ZS50cz84ZjlmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9hcHAvYXBpL25hbWVjaGVhcC9yb3V0ZS50c1xuXG5pbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLy8gLi4uIChpbnRlcmZhY2VzIGFuZCBjb25zdGFudHMgZnJvbSB5b3VyIG9yaWdpbmFsIGZpbGUgcmVtYWluIHRoZSBzYW1lKVxuaW50ZXJmYWNlIE5hbWVjaGVhcERvbWFpblJlc3VsdCB7XG4gIERvbWFpbjogc3RyaW5nO1xuICBBdmFpbGFibGU6IGJvb2xlYW47XG4gIElzUHJlbWl1bU5hbWU6IGJvb2xlYW47XG4gIFByZW1pdW1SZWdpc3RyYXRpb25QcmljZT86IG51bWJlcjtcbiAgUHJlbWl1bVJlbmV3YWxQcmljZT86IG51bWJlcjtcbiAgUHJlbWl1bVJlc3RvcmVQcmljZT86IG51bWJlcjtcbiAgUHJlbWl1bVRyYW5zZmVyUHJpY2U/OiBudW1iZXI7XG4gIEljYW5uRmVlPzogbnVtYmVyO1xuICBFYXBGZWU/OiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBEb21haW5DaGVja1Jlc3VsdCB7XG4gIERvbWFpbjogc3RyaW5nO1xuICBBdmFpbGFibGU6IGJvb2xlYW47XG4gIElzUHJlbWl1bU5hbWU6IGJvb2xlYW47XG4gIFByaWNlPzogbnVtYmVyO1xuICBFcnJvcj86IHN0cmluZztcbn1cblxuY29uc3QgQ09NTU9OX1RMRFMgPSBbXG4gIHsgdGxkOiAnY29tJywgcHJpY2U6IDEyLjk4IH0sXG4gIHsgdGxkOiAnbmV0JywgcHJpY2U6IDE0Ljk4IH0sXG4gIHsgdGxkOiAnb3JnJywgcHJpY2U6IDE0Ljk4IH0sXG4gIHsgdGxkOiAnaW5mbycsIHByaWNlOiAxOC45OCB9LFxuICB7IHRsZDogJ2JpeicsIHByaWNlOiAxOC45OCB9LFxuICB7IHRsZDogJ2NvbS5hdScsIHByaWNlOiAxNi41MCB9LFxuICB7IHRsZDogJ25ldC5hdScsIHByaWNlOiAxNi41MCB9LFxuICB7IHRsZDogJ29yZy5hdScsIHByaWNlOiAxNi41MCB9LFxuICB7IHRsZDogJ2NvJywgcHJpY2U6IDMyLjk4IH0sXG4gIHsgdGxkOiAnaW8nLCBwcmljZTogNTkuOTggfVxuXTtcblxuY29uc3QgVVNEX1RPX0FVRF9SQVRFID0gMS41O1xuXG4vLyAuLi4gKGNoZWNrRG9tYWluQXZhaWxhYmlsaXR5LCBnZW5lcmF0ZURvbWFpblZhcmlhdGlvbnMsIGdldFByaWNlRm9yRG9tYWluIGZ1bmN0aW9ucyBhcmUgdW5jaGFuZ2VkKVxuYXN5bmMgZnVuY3Rpb24gY2hlY2tEb21haW5BdmFpbGFiaWxpdHkoZG9tYWluTGlzdDogc3RyaW5nW10pOiBQcm9taXNlPE5hbWVjaGVhcERvbWFpblJlc3VsdFtdPiB7XG4gIGNvbnN0IGFwaVVzZXIgPSBwcm9jZXNzLmVudi5OQU1FQ0hFQVBfQVBJX1VTRVI7XG4gIGNvbnN0IGFwaUtleSA9IHByb2Nlc3MuZW52Lk5BTUVDSEVBUF9BUElfS0VZO1xuICBjb25zdCB1c2VybmFtZSA9IHByb2Nlc3MuZW52Lk5BTUVDSEVBUF9VU0VSTkFNRTtcbiAgY29uc3QgY2xpZW50SXAgPSBwcm9jZXNzLmVudi5OQU1FQ0hFQVBfQ0xJRU5UX0lQO1xuICBjb25zdCBzYW5kYm94ID0gcHJvY2Vzcy5lbnYuTkFNRUNIRUFQX1NBTkRCT1ggPT09ICd0cnVlJztcblxuICBpZiAoIWFwaVVzZXIgfHwgIWFwaUtleSB8fCAhdXNlcm5hbWUgfHwgIWNsaWVudElwKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIE5hbWVjaGVhcCBBUEkgY29uZmlndXJhdGlvbicpO1xuICB9XG5cbiAgY29uc3QgYmFzZVVybCA9IHNhbmRib3ggXG4gICAgPyAnaHR0cHM6Ly9hcGkuc2FuZGJveC5uYW1lY2hlYXAuY29tL3htbC5yZXNwb25zZSdcbiAgICA6ICdodHRwczovL2FwaS5uYW1lY2hlYXAuY29tL3htbC5yZXNwb25zZSc7XG5cbiAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgQXBpVXNlcjogYXBpVXNlcixcbiAgICBBcGlLZXk6IGFwaUtleSxcbiAgICBVc2VyTmFtZTogdXNlcm5hbWUsXG4gICAgQ29tbWFuZDogJ25hbWVjaGVhcC5kb21haW5zLmNoZWNrJyxcbiAgICBDbGllbnRJcDogY2xpZW50SXAsXG4gICAgRG9tYWluTGlzdDogZG9tYWluTGlzdC5qb2luKCcsJylcbiAgfSk7XG5cbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9PyR7cGFyYW1zLnRvU3RyaW5nKCl9YCwge1xuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWQnIH0sXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgY29uc3QgeG1sVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICBjb25zdCBlcnJvck1hdGNoID0geG1sVGV4dC5tYXRjaCgvPEVycm9yW14+XSo+KFtePF0qKTxcXC9FcnJvcj4vKTtcbiAgICBpZiAoZXJyb3JNYXRjaCkgdGhyb3cgbmV3IEVycm9yKGBOYW1lY2hlYXAgQVBJIGVycm9yOiAke2Vycm9yTWF0Y2hbMV19YCk7XG5cbiAgICBjb25zdCBkb21haW5SZXN1bHRzOiBOYW1lY2hlYXBEb21haW5SZXN1bHRbXSA9IFtdO1xuICAgIGNvbnN0IGRvbWFpblJlZ2V4ID0gLzxEb21haW5DaGVja1Jlc3VsdFtePl0qRG9tYWluPVwiKFteXCJdKilcIltePl0qQXZhaWxhYmxlPVwiKFteXCJdKilcIltePl0qSXNQcmVtaXVtTmFtZT1cIihbXlwiXSopXCJbXj5dKig/OlByZW1pdW1SZWdpc3RyYXRpb25QcmljZT1cIihbXlwiXSopXCIpP1tePl0qXFwvPi9nO1xuICAgIGxldCBtYXRjaDogUmVnRXhwRXhlY0FycmF5IHwgbnVsbDtcbiAgICB3aGlsZSAoKG1hdGNoID0gZG9tYWluUmVnZXguZXhlYyh4bWxUZXh0KSkgIT09IG51bGwpIHtcbiAgICAgIGNvbnN0IFssIGRvbWFpbk5hbWUsIGF2YWlsYWJsZSwgaXNQcmVtaXVtLCBwcmVtaXVtUHJpY2VdID0gbWF0Y2g7XG4gICAgICBjb25zdCByZXN1bHQ6IE5hbWVjaGVhcERvbWFpblJlc3VsdCA9IHtcbiAgICAgICAgRG9tYWluOiBkb21haW5OYW1lLFxuICAgICAgICBBdmFpbGFibGU6IGF2YWlsYWJsZSA9PT0gJ3RydWUnLFxuICAgICAgICBJc1ByZW1pdW1OYW1lOiBpc1ByZW1pdW0gPT09ICd0cnVlJyxcbiAgICAgIH07XG4gICAgICBpZiAocmVzdWx0LklzUHJlbWl1bU5hbWUgJiYgcHJlbWl1bVByaWNlKSByZXN1bHQuUHJlbWl1bVJlZ2lzdHJhdGlvblByaWNlID0gcGFyc2VGbG9hdChwcmVtaXVtUHJpY2UpO1xuICAgICAgZG9tYWluUmVzdWx0cy5wdXNoKHJlc3VsdCk7XG4gICAgfVxuICAgIHJldHVybiBkb21haW5SZXN1bHRzO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ05hbWVjaGVhcCBBUEkgZXJyb3I6JywgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbmZ1bmN0aW9uIGdlbmVyYXRlRG9tYWluVmFyaWF0aW9ucyhiYXNlRG9tYWluOiBzdHJpbmcpOiBzdHJpbmdbXSB7XG4gIGNvbnN0IGRvbWFpbk5hbWUgPSBiYXNlRG9tYWluLnJlcGxhY2UoL1xcLihjb218bmV0fG9yZ3xpbmZvfGJpenxjb21cXC5hdXxuZXRcXC5hdXxvcmdcXC5hdXxjb3xpbykkL2ksICcnKTtcbiAgcmV0dXJuIENPTU1PTl9UTERTLm1hcCgoeyB0bGQgfSkgPT4gYCR7ZG9tYWluTmFtZX0uJHt0bGR9YCk7XG59XG5cbmZ1bmN0aW9uIGdldFByaWNlRm9yRG9tYWluKGRvbWFpbjogc3RyaW5nLCBuYW1lY2hlYXBSZXN1bHQ6IE5hbWVjaGVhcERvbWFpblJlc3VsdCk6IG51bWJlciB7XG4gIGlmIChuYW1lY2hlYXBSZXN1bHQuSXNQcmVtaXVtTmFtZSAmJiBuYW1lY2hlYXBSZXN1bHQuUHJlbWl1bVJlZ2lzdHJhdGlvblByaWNlKSB7XG4gICAgcmV0dXJuIG5hbWVjaGVhcFJlc3VsdC5QcmVtaXVtUmVnaXN0cmF0aW9uUHJpY2UgKiBVU0RfVE9fQVVEX1JBVEUgKyA1O1xuICB9XG4gIGNvbnN0IHRsZCA9IGRvbWFpbi5zcGxpdCgnLicpLnNsaWNlKDEpLmpvaW4oJy4nKTtcbiAgY29uc3QgdGxkSW5mbyA9IENPTU1PTl9UTERTLmZpbmQodCA9PiB0LnRsZCA9PT0gdGxkKTtcbiAgaWYgKHRsZEluZm8pIHJldHVybiB0bGRJbmZvLnByaWNlICogVVNEX1RPX0FVRF9SQVRFICsgNTtcbiAgcmV0dXJuIDIwICogVVNEX1RPX0FVRF9SQVRFICsgNTtcbn1cblxuXG4vLyDinKggTkVXIEZVTkNUSU9OOiBUbyByZWdpc3RlciBhIGRvbWFpbiB3aXRoIE5hbWVjaGVhcFxuYXN5bmMgZnVuY3Rpb24gcmVnaXN0ZXJEb21haW4oZG9tYWluTmFtZTogc3RyaW5nKSB7XG4gIGNvbnN0IGFwaVVzZXIgPSBwcm9jZXNzLmVudi5OQU1FQ0hFQVBfQVBJX1VTRVIhO1xuICBjb25zdCBhcGlLZXkgPSBwcm9jZXNzLmVudi5OQU1FQ0hFQVBfQVBJX0tFWSE7XG4gIGNvbnN0IHVzZXJuYW1lID0gcHJvY2Vzcy5lbnYuTkFNRUNIRUFQX1VTRVJOQU1FITtcbiAgY29uc3QgY2xpZW50SXAgPSBwcm9jZXNzLmVudi5OQU1FQ0hFQVBfQ0xJRU5UX0lQITtcbiAgY29uc3Qgc2FuZGJveCA9IHByb2Nlc3MuZW52Lk5BTUVDSEVBUF9TQU5EQk9YID09PSAndHJ1ZSc7XG5cbiAgY29uc3QgYmFzZVVybCA9IHNhbmRib3ggXG4gICAgPyAnaHR0cHM6Ly9hcGkuc2FuZGJveC5uYW1lY2hlYXAuY29tL3htbC5yZXNwb25zZScgXG4gICAgOiAnaHR0cHM6Ly9hcGkubmFtZWNoZWFwLmNvbS94bWwucmVzcG9uc2UnO1xuXG4gIC8vIFJlZ2lzdHJhbnQgY29udGFjdCBpbmZvcm1hdGlvbiBzb3VyY2VkIGZyb20gZW52aXJvbm1lbnQgdmFyaWFibGVzXG4gIGNvbnN0IHJlZ2lzdHJhbnQgPSB7XG4gICAgRmlyc3ROYW1lOiBwcm9jZXNzLmVudi5SRUdJU1RSQU5UX0ZJUlNUX05BTUUhLFxuICAgIExhc3ROYW1lOiBwcm9jZXNzLmVudi5SRUdJU1RSQU5UX0xBU1RfTkFNRSEsXG4gICAgQWRkcmVzczE6IHByb2Nlc3MuZW52LlJFR0lTVFJBTlRfQUREUkVTUzEhLFxuICAgIENpdHk6IHByb2Nlc3MuZW52LlJFR0lTVFJBTlRfQ0lUWSEsXG4gICAgU3RhdGVQcm92aW5jZTogcHJvY2Vzcy5lbnYuUkVHSVNUUkFOVF9TVEFURV9QUk9WSU5DRSEsXG4gICAgUG9zdGFsQ29kZTogcHJvY2Vzcy5lbnYuUkVHSVNUUkFOVF9QT1NUQUxfQ09ERSEsXG4gICAgQ291bnRyeTogcHJvY2Vzcy5lbnYuUkVHSVNUUkFOVF9DT1VOVFJZISxcbiAgICBQaG9uZTogcHJvY2Vzcy5lbnYuUkVHSVNUUkFOVF9QSE9ORSEsXG4gICAgRW1haWxBZGRyZXNzOiBwcm9jZXNzLmVudi5SRUdJU1RSQU5UX0VNQUlMISxcbiAgfTtcblxuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhyZWdpc3RyYW50KSkge1xuICAgIGlmICghdmFsdWUpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgTWlzc2luZyByZXF1aXJlZCByZWdpc3RyYW50IGVudmlyb25tZW50IHZhcmlhYmxlOiBSRUdJU1RSQU5UXyR7a2V5LnRvVXBwZXJDYXNlKCl9YCk7XG4gICAgfVxuICB9XG5cbiAgLy8g4pyoIEZJWDogQ29ycmVjdGx5IG1hcCB0aGUgcmVnaXN0cmFudCBmaWVsZHMgdG8gdGhlIHJlcXVpcmVkIEFQSSBwYXJhbWV0ZXJzLlxuICAvLyBUaGUgcHJldmlvdXMgY29kZSB1c2VkIGAuLi5yZWdpc3RyYW50YCB3aGljaCByZXN1bHRlZCBpbiBzZW5kaW5nIGBGaXJzdE5hbWVgIGluc3RlYWQgb2YgYFJlZ2lzdHJhbnRGaXJzdE5hbWVgLlxuICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHtcbiAgICBBcGlVc2VyOiBhcGlVc2VyLFxuICAgIEFwaUtleTogYXBpS2V5LFxuICAgIFVzZXJOYW1lOiB1c2VybmFtZSxcbiAgICBDb21tYW5kOiAnbmFtZWNoZWFwLmRvbWFpbnMuY3JlYXRlJyxcbiAgICBDbGllbnRJcDogY2xpZW50SXAsXG4gICAgRG9tYWluTmFtZTogZG9tYWluTmFtZSxcbiAgICBZZWFyczogJzEnLFxuICAgIC8vIFJlZ2lzdHJhbnQgY29udGFjdCBkZXRhaWxzXG4gICAgUmVnaXN0cmFudEZpcnN0TmFtZTogcmVnaXN0cmFudC5GaXJzdE5hbWUsXG4gICAgUmVnaXN0cmFudExhc3ROYW1lOiByZWdpc3RyYW50Lkxhc3ROYW1lLFxuICAgIFJlZ2lzdHJhbnRBZGRyZXNzMTogcmVnaXN0cmFudC5BZGRyZXNzMSxcbiAgICBSZWdpc3RyYW50Q2l0eTogcmVnaXN0cmFudC5DaXR5LFxuICAgIFJlZ2lzdHJhbnRTdGF0ZVByb3ZpbmNlOiByZWdpc3RyYW50LlN0YXRlUHJvdmluY2UsXG4gICAgUmVnaXN0cmFudFBvc3RhbENvZGU6IHJlZ2lzdHJhbnQuUG9zdGFsQ29kZSxcbiAgICBSZWdpc3RyYW50Q291bnRyeTogcmVnaXN0cmFudC5Db3VudHJ5LFxuICAgIFJlZ2lzdHJhbnRQaG9uZTogcmVnaXN0cmFudC5QaG9uZSxcbiAgICBSZWdpc3RyYW50RW1haWxBZGRyZXNzOiByZWdpc3RyYW50LkVtYWlsQWRkcmVzcyxcbiAgICAvLyBBZG1pbiwgVGVjaCwgYW5kIEF1eEJpbGxpbmcgY29udGFjdHMgKHVzaW5nIHRoZSBzYW1lIGRldGFpbHMpXG4gICAgQWRtaW5GaXJzdE5hbWU6IHJlZ2lzdHJhbnQuRmlyc3ROYW1lLFxuICAgIEFkbWluTGFzdE5hbWU6IHJlZ2lzdHJhbnQuTGFzdE5hbWUsXG4gICAgQWRtaW5BZGRyZXNzMTogcmVnaXN0cmFudC5BZGRyZXNzMSxcbiAgICBBZG1pbkNpdHk6IHJlZ2lzdHJhbnQuQ2l0eSxcbiAgICBBZG1pblN0YXRlUHJvdmluY2U6IHJlZ2lzdHJhbnQuU3RhdGVQcm92aW5jZSxcbiAgICBBZG1pblBvc3RhbENvZGU6IHJlZ2lzdHJhbnQuUG9zdGFsQ29kZSxcbiAgICBBZG1pbkNvdW50cnk6IHJlZ2lzdHJhbnQuQ291bnRyeSxcbiAgICBBZG1pblBob25lOiByZWdpc3RyYW50LlBob25lLFxuICAgIEFkbWluRW1haWxBZGRyZXNzOiByZWdpc3RyYW50LkVtYWlsQWRkcmVzcyxcbiAgICBUZWNoRmlyc3ROYW1lOiByZWdpc3RyYW50LkZpcnN0TmFtZSxcbiAgICBUZWNoTGFzdE5hbWU6IHJlZ2lzdHJhbnQuTGFzdE5hbWUsXG4gICAgVGVjaEFkZHJlc3MxOiByZWdpc3RyYW50LkFkZHJlc3MxLFxuICAgIFRlY2hDaXR5OiByZWdpc3RyYW50LkNpdHksXG4gICAgVGVjaFN0YXRlUHJvdmluY2U6IHJlZ2lzdHJhbnQuU3RhdGVQcm92aW5jZSxcbiAgICBUZWNoUG9zdGFsQ29kZTogcmVnaXN0cmFudC5Qb3N0YWxDb2RlLFxuICAgIFRlY2hDb3VudHJ5OiByZWdpc3RyYW50LkNvdW50cnksXG4gICAgVGVjaFBob25lOiByZWdpc3RyYW50LlBob25lLFxuICAgIFRlY2hFbWFpbEFkZHJlc3M6IHJlZ2lzdHJhbnQuRW1haWxBZGRyZXNzLFxuICAgIEF1eEJpbGxpbmdGaXJzdE5hbWU6IHJlZ2lzdHJhbnQuRmlyc3ROYW1lLFxuICAgIEF1eEJpbGxpbmdMYXN0TmFtZTogcmVnaXN0cmFudC5MYXN0TmFtZSxcbiAgICBBdXhCaWxsaW5nQWRkcmVzczE6IHJlZ2lzdHJhbnQuQWRkcmVzczEsXG4gICAgQXV4QmlsbGluZ0NpdHk6IHJlZ2lzdHJhbnQuQ2l0eSxcbiAgICBBdXhCaWxsaW5nU3RhdGVQcm92aW5jZTogcmVnaXN0cmFudC5TdGF0ZVByb3ZpbmNlLFxuICAgIEF1eEJpbGxpbmdQb3N0YWxDb2RlOiByZWdpc3RyYW50LlBvc3RhbENvZGUsXG4gICAgQXV4QmlsbGluZ0NvdW50cnk6IHJlZ2lzdHJhbnQuQ291bnRyeSxcbiAgICBBdXhCaWxsaW5nUGhvbmU6IHJlZ2lzdHJhbnQuUGhvbmUsXG4gICAgQXV4QmlsbGluZ0VtYWlsQWRkcmVzczogcmVnaXN0cmFudC5FbWFpbEFkZHJlc3MsXG4gIH0pO1xuXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfT8ke3BhcmFtcy50b1N0cmluZygpfWAsIHsgbWV0aG9kOiAnUE9TVCcgfSk7XG4gICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yISBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuXG4gICAgY29uc3QgeG1sVGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICBcbiAgICBjb25zdCBlcnJvck1hdGNoID0geG1sVGV4dC5tYXRjaCgvPEVycm9yW14+XSo+KFtePF0qKTxcXC9FcnJvcj4vKTtcbiAgICBpZiAoZXJyb3JNYXRjaCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBOYW1lY2hlYXAgQVBJIEVycm9yOiAke2Vycm9yTWF0Y2hbMV19YCk7XG4gICAgfVxuXG4gICAgY29uc3Qgc3VjY2Vzc01hdGNoID0geG1sVGV4dC5tYXRjaCgvPERvbWFpbkNyZWF0ZVJlc3VsdFtePl0qUmVnaXN0ZXJlZD1cInRydWVcIltePl0qPi8pO1xuICAgIGlmICghc3VjY2Vzc01hdGNoKSB7XG4gICAgICAvLyBQcm92aWRlIGEgbW9yZSBkZXRhaWxlZCBlcnJvciBpZiByZWdpc3RyYXRpb24gaXMgbm90IGV4cGxpY2l0bHkgY29uZmlybWVkXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYERvbWFpbiByZWdpc3RyYXRpb24gZmFpbGVkLiBUaGUgcmVzcG9uc2UgZnJvbSBOYW1lY2hlYXAgZGlkIG5vdCBjb25maXJtIHN1Y2Nlc3MuIFhNTDogJHt4bWxUZXh0fWApO1xuICAgIH1cblxuICAgIGNvbnN0IG9yZGVySWRNYXRjaCA9IHhtbFRleHQubWF0Y2goL09yZGVySUQ9XCIoXFxkKylcIi8pO1xuICAgIGNvbnN0IHRyYW5zYWN0aW9uSWRNYXRjaCA9IHhtbFRleHQubWF0Y2goL1RyYW5zYWN0aW9uSUQ9XCIoXFxkKylcIi8pO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiBgRG9tYWluICR7ZG9tYWluTmFtZX0gcmVnaXN0ZXJlZCBzdWNjZXNzZnVsbHkuYCxcbiAgICAgIG9yZGVySWQ6IG9yZGVySWRNYXRjaCA/IG9yZGVySWRNYXRjaFsxXSA6IG51bGwsXG4gICAgICB0cmFuc2FjdGlvbklkOiB0cmFuc2FjdGlvbklkTWF0Y2ggPyB0cmFuc2FjdGlvbklkTWF0Y2hbMV0gOiBudWxsLFxuICAgIH07XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignTmFtZWNoZWFwIHJlZ2lzdHJhdGlvbiBlcnJvcjonLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLy8gSGVscGVyIHRvIGdldCBTdXBhYmFzZSBjbGllbnQgKHNlcnZpY2Ugcm9sZSlcbmZ1bmN0aW9uIGdldFN1cGFiYXNlU2VydmljZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhXG4gICk7XG59XG5cbi8vIEZ1bmN0aW9uIHRvIHNldCBDTkFNRSByZWNvcmQgdmlhIE5hbWVjaGVhcCBBUElcbmFzeW5jIGZ1bmN0aW9uIHNldENOQU1FUmVjb3JkKGRvbWFpbjogc3RyaW5nLCBjbmFtZVZhbHVlOiBzdHJpbmcpIHtcbiAgY29uc3QgYXBpVXNlciA9IHByb2Nlc3MuZW52Lk5BTUVDSEVBUF9BUElfVVNFUiE7XG4gIGNvbnN0IGFwaUtleSA9IHByb2Nlc3MuZW52Lk5BTUVDSEVBUF9BUElfS0VZITtcbiAgY29uc3QgdXNlcm5hbWUgPSBwcm9jZXNzLmVudi5OQU1FQ0hFQVBfVVNFUk5BTUUhO1xuICBjb25zdCBjbGllbnRJcCA9IHByb2Nlc3MuZW52Lk5BTUVDSEVBUF9DTElFTlRfSVAhO1xuICBjb25zdCBzYW5kYm94ID0gcHJvY2Vzcy5lbnYuTkFNRUNIRUFQX1NBTkRCT1ggPT09ICd0cnVlJztcbiAgY29uc3QgYmFzZVVybCA9IHNhbmRib3hcbiAgICA/ICdodHRwczovL2FwaS5zYW5kYm94Lm5hbWVjaGVhcC5jb20veG1sLnJlc3BvbnNlJ1xuICAgIDogJ2h0dHBzOi8vYXBpLm5hbWVjaGVhcC5jb20veG1sLnJlc3BvbnNlJztcblxuICAvLyBOYW1lY2hlYXAgQVBJIGZvciBETlMgc2V0SG9zdHNcbiAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgQXBpVXNlcjogYXBpVXNlcixcbiAgICBBcGlLZXk6IGFwaUtleSxcbiAgICBVc2VyTmFtZTogdXNlcm5hbWUsXG4gICAgQ29tbWFuZDogJ25hbWVjaGVhcC5kb21haW5zLmRucy5zZXRIb3N0cycsXG4gICAgQ2xpZW50SXA6IGNsaWVudElwLFxuICAgIFNMRDogZG9tYWluLnNwbGl0KCcuJylbMF0sXG4gICAgVExEOiBkb21haW4uc3BsaXQoJy4nKS5zbGljZSgxKS5qb2luKCcuJyksXG4gICAgJ0hvc3ROYW1lMSc6ICdAJyxcbiAgICAnUmVjb3JkVHlwZTEnOiAnQ05BTUUnLFxuICAgICdBZGRyZXNzMSc6IGNuYW1lVmFsdWUsXG4gICAgJ1RUTDEnOiAnMTgwMCcsXG4gIH0pO1xuXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0/JHtwYXJhbXMudG9TdHJpbmcoKX1gLCB7IG1ldGhvZDogJ1BPU1QnIH0pO1xuICBjb25zdCB4bWxUZXh0ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICBjb25zdCBlcnJvck1hdGNoID0geG1sVGV4dC5tYXRjaCgvPEVycm9yW14+XSo+KFtePF0qKTxcXC9FcnJvcj4vKTtcbiAgaWYgKGVycm9yTWF0Y2gpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYE5hbWVjaGVhcCBETlMgRXJyb3I6ICR7ZXJyb3JNYXRjaFsxXX1gKTtcbiAgfVxuICBjb25zdCBzdWNjZXNzTWF0Y2ggPSB4bWxUZXh0Lm1hdGNoKC88RG9tYWluRE5TU2V0SG9zdHNSZXN1bHRbXj5dKklzU3VjY2Vzcz1cInRydWVcIi8pO1xuICBpZiAoIXN1Y2Nlc3NNYXRjaCkge1xuICAgIHRocm93IG5ldyBFcnJvcihgQ05BTUUgbWFwcGluZyBmYWlsZWQuIFhNTDogJHt4bWxUZXh0fWApO1xuICB9XG4gIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIG1lc3NhZ2U6IGBDTkFNRSBmb3IgJHtkb21haW59IC0+ICR7Y25hbWVWYWx1ZX0gc2V0IHN1Y2Nlc3NmdWxseS5gIH07XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVxdWVzdEJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBjb25zdCB7IGRvbWFpbiwgYWN0aW9uLCBzaXRlSWQsIHVzZXJJZCwgc3RyaXBlU2Vzc2lvbklkIH0gPSByZXF1ZXN0Qm9keTtcblxuICAgIGlmICghZG9tYWluKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0RvbWFpbiBpcyByZXF1aXJlZCcgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICBpZiAoYWN0aW9uID09PSAnY2hlY2snKSB7XG4gICAgICBjb25zdCBkb21haW5WYXJpYXRpb25zID0gZ2VuZXJhdGVEb21haW5WYXJpYXRpb25zKGRvbWFpbik7XG4gICAgICBjb25zdCBuYW1lY2hlYXBSZXN1bHRzID0gYXdhaXQgY2hlY2tEb21haW5BdmFpbGFiaWxpdHkoZG9tYWluVmFyaWF0aW9ucyk7XG4gICAgICBjb25zdCByZXN1bHRzOiBEb21haW5DaGVja1Jlc3VsdFtdID0gbmFtZWNoZWFwUmVzdWx0cy5tYXAocmVzdWx0ID0+IHtcbiAgICAgICAgY29uc3QgZm9ybWF0dGVkUmVzdWx0OiBEb21haW5DaGVja1Jlc3VsdCA9IHtcbiAgICAgICAgICBEb21haW46IHJlc3VsdC5Eb21haW4sXG4gICAgICAgICAgQXZhaWxhYmxlOiByZXN1bHQuQXZhaWxhYmxlLFxuICAgICAgICAgIElzUHJlbWl1bU5hbWU6IHJlc3VsdC5Jc1ByZW1pdW1OYW1lLFxuICAgICAgICB9O1xuICAgICAgICBpZiAocmVzdWx0LkF2YWlsYWJsZSkge1xuICAgICAgICAgIGZvcm1hdHRlZFJlc3VsdC5QcmljZSA9IGdldFByaWNlRm9yRG9tYWluKHJlc3VsdC5Eb21haW4sIHJlc3VsdCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZvcm1hdHRlZFJlc3VsdDtcbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgcmVzdWx0cyB9KTtcbiAgICB9XG5cbiAgICBpZiAoYWN0aW9uID09PSAncmVnaXN0ZXInKSB7XG4gICAgICBpZiAoIXVzZXJJZCkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ3VzZXJJZCBpcyByZXF1aXJlZCBmb3IgcmVnaXN0cmF0aW9uJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBzdXBhYmFzZSA9IGdldFN1cGFiYXNlU2VydmljZUNsaWVudCgpO1xuXG4gICAgICAvLyBIYW5kbGUgc2l0ZSBtYXBwaW5nIC0gaWYgc2l0ZUlkIGlzICdwZW5kaW5nJywgd2UnbGwgcmVnaXN0ZXIgdGhlIGRvbWFpbiB3aXRob3V0IHNpdGUgbWFwcGluZ1xuICAgICAgbGV0IHNpdGVOYW1lID0gbnVsbDtcbiAgICAgIGxldCBmaW5hbFNpdGVJZCA9IG51bGw7XG5cbiAgICAgIGlmIChzaXRlSWQgJiYgc2l0ZUlkICE9PSAncGVuZGluZycpIHtcbiAgICAgICAgLy8gRmV0Y2ggc2l0ZV9uYW1lIGZyb20gU3VwYWJhc2VcbiAgICAgICAgY29uc29sZS5sb2coJ1tOYW1lY2hlYXAgQVBJXSBGZXRjaGluZyBzaXRlX25hbWUgZm9yIHNpdGVJZDonLCBzaXRlSWQpO1xuICAgICAgICBjb25zdCB7IGRhdGE6IHNpdGUsIGVycm9yOiBzaXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ3VzZXItd2Vic2l0ZXMnKVxuICAgICAgICAgIC5zZWxlY3QoJ3NpdGVfbmFtZScpXG4gICAgICAgICAgLmVxKCdpZCcsIHNpdGVJZClcbiAgICAgICAgICAuc2luZ2xlKCk7XG4gICAgICAgIGlmIChzaXRlRXJyb3IgfHwgIXNpdGUpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdbTmFtZWNoZWFwIEFQSV0gQ291bGQgbm90IGZldGNoIHNpdGVfbmFtZSBmb3Igc2l0ZUlkOicsIHNpdGVJZCwgc2l0ZUVycm9yKTtcbiAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0NvdWxkIG5vdCBmZXRjaCBzaXRlX25hbWUgZm9yIHNpdGVJZCcgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICAgICAgfVxuICAgICAgICBzaXRlTmFtZSA9IHNpdGUuc2l0ZV9uYW1lO1xuICAgICAgICBmaW5hbFNpdGVJZCA9IHNpdGVJZDtcbiAgICAgICAgY29uc29sZS5sb2coJ1tOYW1lY2hlYXAgQVBJXSBzaXRlX25hbWUgZmV0Y2hlZDonLCBzaXRlTmFtZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygnW05hbWVjaGVhcCBBUEldIFJlZ2lzdGVyaW5nIGRvbWFpbiB3aXRob3V0IHNpdGUgbWFwcGluZyAoc2l0ZUlkIGlzIHBlbmRpbmcpJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIENyZWF0ZSBvciB1cGRhdGUgZG9tYWluIHJlY29yZCBpbiBkYXRhYmFzZVxuICAgICAgY29uc3QgZG9tYWluRGF0YSA9IHtcbiAgICAgICAgZG9tYWluX25hbWU6IGRvbWFpbixcbiAgICAgICAgdXNlcl9pZDogdXNlcklkLFxuICAgICAgICBzaXRlX2lkOiBmaW5hbFNpdGVJZCxcbiAgICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICAgIHN0cmlwZV9zZXNzaW9uX2lkOiBzdHJpcGVTZXNzaW9uSWQsXG4gICAgICAgIGNuYW1lX3RhcmdldDogc2l0ZU5hbWUsXG4gICAgICB9O1xuXG4gICAgICAvLyBDaGVjayBpZiBkb21haW4gcmVjb3JkIGFscmVhZHkgZXhpc3RzXG4gICAgICBjb25zdCB7IGRhdGE6IGV4aXN0aW5nRG9tYWluIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgnZG9tYWlucycpXG4gICAgICAgIC5zZWxlY3QoJ2lkJylcbiAgICAgICAgLmVxKCdkb21haW5fbmFtZScsIGRvbWFpbilcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKVxuICAgICAgICAuc2luZ2xlKCk7XG5cbiAgICAgIGxldCBkb21haW5SZWNvcmQ6IGFueTtcbiAgICAgIGlmIChleGlzdGluZ0RvbWFpbikge1xuICAgICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgZG9tYWluIHJlY29yZFxuICAgICAgICBjb25zdCB7IGRhdGEsIGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnZG9tYWlucycpXG4gICAgICAgICAgLnVwZGF0ZSh7IC4uLmRvbWFpbkRhdGEsIHN0YXR1czogJ3BlbmRpbmcnIH0pXG4gICAgICAgICAgLmVxKCdpZCcsIGV4aXN0aW5nRG9tYWluLmlkKVxuICAgICAgICAgIC5zZWxlY3QoKVxuICAgICAgICAgIC5zaW5nbGUoKTtcblxuICAgICAgICBpZiAodXBkYXRlRXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdbTmFtZWNoZWFwIEFQSV0gRmFpbGVkIHRvIHVwZGF0ZSBkb21haW4gcmVjb3JkOicsIHVwZGF0ZUVycm9yKTtcbiAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byB1cGRhdGUgZG9tYWluIHJlY29yZCcgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgICAgICAgfVxuICAgICAgICBkb21haW5SZWNvcmQgPSBkYXRhO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gQ3JlYXRlIG5ldyBkb21haW4gcmVjb3JkXG4gICAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3I6IGluc2VydEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKCdkb21haW5zJylcbiAgICAgICAgICAuaW5zZXJ0KGRvbWFpbkRhdGEpXG4gICAgICAgICAgLnNlbGVjdCgpXG4gICAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICAgIGlmIChpbnNlcnRFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tOYW1lY2hlYXAgQVBJXSBGYWlsZWQgdG8gY3JlYXRlIGRvbWFpbiByZWNvcmQ6JywgaW5zZXJ0RXJyb3IpO1xuICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnRmFpbGVkIHRvIGNyZWF0ZSBkb21haW4gcmVjb3JkJyB9LCB7IHN0YXR1czogNTAwIH0pO1xuICAgICAgICB9XG4gICAgICAgIGRvbWFpblJlY29yZCA9IGRhdGE7XG4gICAgICB9XG5cbiAgICAgIC8vIFJlZ2lzdGVyIGRvbWFpbiB3aXRoIE5hbWVjaGVhcFxuICAgICAgY29uc29sZS5sb2coJ1tOYW1lY2hlYXAgQVBJXSBSZWdpc3RlcmluZyBkb21haW46JywgZG9tYWluKTtcbiAgICAgIGxldCByZWdpc3RyYXRpb25SZXN1bHQ6IGFueTtcbiAgICAgIHRyeSB7XG4gICAgICAgIHJlZ2lzdHJhdGlvblJlc3VsdCA9IGF3YWl0IHJlZ2lzdGVyRG9tYWluKGRvbWFpbik7XG4gICAgICAgIGNvbnNvbGUubG9nKCdbTmFtZWNoZWFwIEFQSV0gUmVnaXN0cmF0aW9uIHJlc3VsdDonLCByZWdpc3RyYXRpb25SZXN1bHQpO1xuXG4gICAgICAgIC8vIFVwZGF0ZSBkb21haW4gcmVjb3JkIHdpdGggcmVnaXN0cmF0aW9uIGRldGFpbHNcbiAgICAgICAgY29uc3QgcmVnaXN0cmF0aW9uRGF0ZSA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcbiAgICAgICAgY29uc3QgZXhwaXJ5RGF0ZSA9IG5ldyBEYXRlKCk7XG4gICAgICAgIGV4cGlyeURhdGUuc2V0RnVsbFllYXIoZXhwaXJ5RGF0ZS5nZXRGdWxsWWVhcigpICsgMSk7IC8vIDEgeWVhciBmcm9tIG5vd1xuXG4gICAgICAgIGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgLmZyb20oJ2RvbWFpbnMnKVxuICAgICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgICAgc3RhdHVzOiAncmVnaXN0ZXJlZCcsXG4gICAgICAgICAgICByZWdpc3RyYXRpb25fZGF0ZTogcmVnaXN0cmF0aW9uRGF0ZSxcbiAgICAgICAgICAgIGV4cGlyeV9kYXRlOiBleHBpcnlEYXRlLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICBuYW1lY2hlYXBfb3JkZXJfaWQ6IHJlZ2lzdHJhdGlvblJlc3VsdC5vcmRlcklkLFxuICAgICAgICAgICAgbmFtZWNoZWFwX3RyYW5zYWN0aW9uX2lkOiByZWdpc3RyYXRpb25SZXN1bHQudHJhbnNhY3Rpb25JZCxcbiAgICAgICAgICB9KVxuICAgICAgICAgIC5lcSgnaWQnLCBkb21haW5SZWNvcmQuaWQpO1xuXG4gICAgICB9IGNhdGNoIChyZWdFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbTmFtZWNoZWFwIEFQSV0gUmVnaXN0cmF0aW9uIGZhaWxlZDonLCByZWdFcnJvcik7XG5cbiAgICAgICAgLy8gVXBkYXRlIGRvbWFpbiByZWNvcmQgdG8gZmFpbGVkIHN0YXR1c1xuICAgICAgICBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKCdkb21haW5zJylcbiAgICAgICAgICAudXBkYXRlKHsgc3RhdHVzOiAnZmFpbGVkJyB9KVxuICAgICAgICAgIC5lcSgnaWQnLCBkb21haW5SZWNvcmQuaWQpO1xuXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgZXJyb3I6IHJlZ0Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyByZWdFcnJvci5tZXNzYWdlIDogJ0RvbWFpbiByZWdpc3RyYXRpb24gZmFpbGVkJ1xuICAgICAgICB9LCB7IHN0YXR1czogNTAwIH0pO1xuICAgICAgfVxuXG4gICAgICAvLyBTZXQgQ05BTUUgcmVjb3JkIGFuZCB1cGRhdGUgc2l0ZSBtYXBwaW5nIG9ubHkgaWYgd2UgaGF2ZSBhIHNpdGVcbiAgICAgIGxldCBjbmFtZVJlc3VsdCA9IG51bGw7XG4gICAgICBsZXQgc2l0ZU5hbWVVcGRhdGVkID0gZmFsc2U7XG5cbiAgICAgIGlmIChzaXRlTmFtZSAmJiBmaW5hbFNpdGVJZCkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdbTmFtZWNoZWFwIEFQSV0gU2V0dGluZyBDTkFNRSBmb3IgZG9tYWluOicsIGRvbWFpbiwgJ3RvOicsIHNpdGVOYW1lKTtcbiAgICAgICAgICBjbmFtZVJlc3VsdCA9IGF3YWl0IHNldENOQU1FUmVjb3JkKGRvbWFpbiwgc2l0ZU5hbWUpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdbTmFtZWNoZWFwIEFQSV0gQ05BTUUgbWFwcGluZyByZXN1bHQ6JywgY25hbWVSZXN1bHQpO1xuXG4gICAgICAgICAgLy8gVXBkYXRlIGRvbWFpbiByZWNvcmQgd2l0aCBETlMgY29uZmlndXJhdGlvblxuICAgICAgICAgIGF3YWl0IHN1cGFiYXNlXG4gICAgICAgICAgICAuZnJvbSgnZG9tYWlucycpXG4gICAgICAgICAgICAudXBkYXRlKHtcbiAgICAgICAgICAgICAgZG5zX2NvbmZpZ3VyZWQ6IHRydWUsXG4gICAgICAgICAgICAgIHN0YXR1czogJ2FjdGl2ZSdcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAuZXEoJ2lkJywgZG9tYWluUmVjb3JkLmlkKTtcblxuICAgICAgICAgIC8vIFVwZGF0ZSBzaXRlX25hbWUgdG8gdGhlIG5ldyBkb21haW5cbiAgICAgICAgICBjb25zb2xlLmxvZygnW05hbWVjaGVhcCBBUEldIFVwZGF0aW5nIHNpdGVfbmFtZSBmb3Igc2l0ZUlkOicsIGZpbmFsU2l0ZUlkLCAndG86JywgZG9tYWluKTtcbiAgICAgICAgICBjb25zdCB7IGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAgIC5mcm9tKCd1c2VyLXdlYnNpdGVzJylcbiAgICAgICAgICAgIC51cGRhdGUoeyBzaXRlX25hbWU6IGRvbWFpbiB9KVxuICAgICAgICAgICAgLmVxKCdpZCcsIGZpbmFsU2l0ZUlkKTtcbiAgICAgICAgICBpZiAodXBkYXRlRXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tOYW1lY2hlYXAgQVBJXSBGYWlsZWQgdG8gdXBkYXRlIHNpdGVfbmFtZTonLCB1cGRhdGVFcnJvcik7XG4gICAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgICAgICAuLi5yZWdpc3RyYXRpb25SZXN1bHQsXG4gICAgICAgICAgICAgIGNuYW1lUmVzdWx0LFxuICAgICAgICAgICAgICB1cGRhdGVFcnJvcjogdXBkYXRlRXJyb3IubWVzc2FnZSxcbiAgICAgICAgICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGNvbnNvbGUubG9nKCdbTmFtZWNoZWFwIEFQSV0gc2l0ZV9uYW1lIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5LicpO1xuICAgICAgICAgIHNpdGVOYW1lVXBkYXRlZCA9IHRydWU7XG5cbiAgICAgICAgfSBjYXRjaCAoY25hbWVFcnIpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdbTmFtZWNoZWFwIEFQSV0gQ05BTUUgbWFwcGluZyBlcnJvcjonLCBjbmFtZUVycik7XG4gICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICAgIC4uLnJlZ2lzdHJhdGlvblJlc3VsdCxcbiAgICAgICAgICAgIGNuYW1lRXJyb3I6IGNuYW1lRXJyIGluc3RhbmNlb2YgRXJyb3IgPyBjbmFtZUVyci5tZXNzYWdlIDogY25hbWVFcnIsXG4gICAgICAgICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1tOYW1lY2hlYXAgQVBJXSBTa2lwcGluZyBDTkFNRSBzZXR1cCAtIG5vIHNpdGUgbWFwcGluZyBwcm92aWRlZCcpO1xuICAgICAgICAvLyBVcGRhdGUgZG9tYWluIHN0YXR1cyB0byByZWdpc3RlcmVkIChub3QgYWN0aXZlIHNpbmNlIG5vIEROUyBzZXR1cClcbiAgICAgICAgYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnZG9tYWlucycpXG4gICAgICAgICAgLnVwZGF0ZSh7IHN0YXR1czogJ3JlZ2lzdGVyZWQnIH0pXG4gICAgICAgICAgLmVxKCdpZCcsIGRvbWFpblJlY29yZC5pZCk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIC4uLnJlZ2lzdHJhdGlvblJlc3VsdCxcbiAgICAgICAgY25hbWVSZXN1bHQsXG4gICAgICAgIHNpdGVOYW1lVXBkYXRlZCxcbiAgICAgICAgZG9tYWluUmVjb3JkLFxuICAgICAgICBtZXNzYWdlOiBzaXRlTmFtZSA/ICdEb21haW4gcmVnaXN0ZXJlZCBhbmQgbWFwcGVkIHRvIHNpdGUnIDogJ0RvbWFpbiByZWdpc3RlcmVkIHN1Y2Nlc3NmdWxseSAobm8gc2l0ZSBtYXBwaW5nKSdcbiAgICAgIH0pO1xuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnSW52YWxpZCBhY3Rpb24nIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignQVBJIGVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59Il0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNyZWF0ZUNsaWVudCIsIkNPTU1PTl9UTERTIiwidGxkIiwicHJpY2UiLCJVU0RfVE9fQVVEX1JBVEUiLCJjaGVja0RvbWFpbkF2YWlsYWJpbGl0eSIsImRvbWFpbkxpc3QiLCJhcGlVc2VyIiwicHJvY2VzcyIsImVudiIsIk5BTUVDSEVBUF9BUElfVVNFUiIsImFwaUtleSIsIk5BTUVDSEVBUF9BUElfS0VZIiwidXNlcm5hbWUiLCJOQU1FQ0hFQVBfVVNFUk5BTUUiLCJjbGllbnRJcCIsIk5BTUVDSEVBUF9DTElFTlRfSVAiLCJzYW5kYm94IiwiTkFNRUNIRUFQX1NBTkRCT1giLCJFcnJvciIsImJhc2VVcmwiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJBcGlVc2VyIiwiQXBpS2V5IiwiVXNlck5hbWUiLCJDb21tYW5kIiwiQ2xpZW50SXAiLCJEb21haW5MaXN0Iiwiam9pbiIsInJlc3BvbnNlIiwiZmV0Y2giLCJ0b1N0cmluZyIsIm1ldGhvZCIsImhlYWRlcnMiLCJvayIsInN0YXR1cyIsInhtbFRleHQiLCJ0ZXh0IiwiZXJyb3JNYXRjaCIsIm1hdGNoIiwiZG9tYWluUmVzdWx0cyIsImRvbWFpblJlZ2V4IiwiZXhlYyIsImRvbWFpbk5hbWUiLCJhdmFpbGFibGUiLCJpc1ByZW1pdW0iLCJwcmVtaXVtUHJpY2UiLCJyZXN1bHQiLCJEb21haW4iLCJBdmFpbGFibGUiLCJJc1ByZW1pdW1OYW1lIiwiUHJlbWl1bVJlZ2lzdHJhdGlvblByaWNlIiwicGFyc2VGbG9hdCIsInB1c2giLCJlcnJvciIsImNvbnNvbGUiLCJnZW5lcmF0ZURvbWFpblZhcmlhdGlvbnMiLCJiYXNlRG9tYWluIiwicmVwbGFjZSIsIm1hcCIsImdldFByaWNlRm9yRG9tYWluIiwiZG9tYWluIiwibmFtZWNoZWFwUmVzdWx0Iiwic3BsaXQiLCJzbGljZSIsInRsZEluZm8iLCJmaW5kIiwidCIsInJlZ2lzdGVyRG9tYWluIiwicmVnaXN0cmFudCIsIkZpcnN0TmFtZSIsIlJFR0lTVFJBTlRfRklSU1RfTkFNRSIsIkxhc3ROYW1lIiwiUkVHSVNUUkFOVF9MQVNUX05BTUUiLCJBZGRyZXNzMSIsIlJFR0lTVFJBTlRfQUREUkVTUzEiLCJDaXR5IiwiUkVHSVNUUkFOVF9DSVRZIiwiU3RhdGVQcm92aW5jZSIsIlJFR0lTVFJBTlRfU1RBVEVfUFJPVklOQ0UiLCJQb3N0YWxDb2RlIiwiUkVHSVNUUkFOVF9QT1NUQUxfQ09ERSIsIkNvdW50cnkiLCJSRUdJU1RSQU5UX0NPVU5UUlkiLCJQaG9uZSIsIlJFR0lTVFJBTlRfUEhPTkUiLCJFbWFpbEFkZHJlc3MiLCJSRUdJU1RSQU5UX0VNQUlMIiwia2V5IiwidmFsdWUiLCJPYmplY3QiLCJlbnRyaWVzIiwidG9VcHBlckNhc2UiLCJEb21haW5OYW1lIiwiWWVhcnMiLCJSZWdpc3RyYW50Rmlyc3ROYW1lIiwiUmVnaXN0cmFudExhc3ROYW1lIiwiUmVnaXN0cmFudEFkZHJlc3MxIiwiUmVnaXN0cmFudENpdHkiLCJSZWdpc3RyYW50U3RhdGVQcm92aW5jZSIsIlJlZ2lzdHJhbnRQb3N0YWxDb2RlIiwiUmVnaXN0cmFudENvdW50cnkiLCJSZWdpc3RyYW50UGhvbmUiLCJSZWdpc3RyYW50RW1haWxBZGRyZXNzIiwiQWRtaW5GaXJzdE5hbWUiLCJBZG1pbkxhc3ROYW1lIiwiQWRtaW5BZGRyZXNzMSIsIkFkbWluQ2l0eSIsIkFkbWluU3RhdGVQcm92aW5jZSIsIkFkbWluUG9zdGFsQ29kZSIsIkFkbWluQ291bnRyeSIsIkFkbWluUGhvbmUiLCJBZG1pbkVtYWlsQWRkcmVzcyIsIlRlY2hGaXJzdE5hbWUiLCJUZWNoTGFzdE5hbWUiLCJUZWNoQWRkcmVzczEiLCJUZWNoQ2l0eSIsIlRlY2hTdGF0ZVByb3ZpbmNlIiwiVGVjaFBvc3RhbENvZGUiLCJUZWNoQ291bnRyeSIsIlRlY2hQaG9uZSIsIlRlY2hFbWFpbEFkZHJlc3MiLCJBdXhCaWxsaW5nRmlyc3ROYW1lIiwiQXV4QmlsbGluZ0xhc3ROYW1lIiwiQXV4QmlsbGluZ0FkZHJlc3MxIiwiQXV4QmlsbGluZ0NpdHkiLCJBdXhCaWxsaW5nU3RhdGVQcm92aW5jZSIsIkF1eEJpbGxpbmdQb3N0YWxDb2RlIiwiQXV4QmlsbGluZ0NvdW50cnkiLCJBdXhCaWxsaW5nUGhvbmUiLCJBdXhCaWxsaW5nRW1haWxBZGRyZXNzIiwic3VjY2Vzc01hdGNoIiwib3JkZXJJZE1hdGNoIiwidHJhbnNhY3Rpb25JZE1hdGNoIiwic3VjY2VzcyIsIm1lc3NhZ2UiLCJvcmRlcklkIiwidHJhbnNhY3Rpb25JZCIsImdldFN1cGFiYXNlU2VydmljZUNsaWVudCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJzZXRDTkFNRVJlY29yZCIsImNuYW1lVmFsdWUiLCJTTEQiLCJUTEQiLCJQT1NUIiwicmVxdWVzdCIsInJlcXVlc3RCb2R5IiwianNvbiIsImFjdGlvbiIsInNpdGVJZCIsInVzZXJJZCIsInN0cmlwZVNlc3Npb25JZCIsImRvbWFpblZhcmlhdGlvbnMiLCJuYW1lY2hlYXBSZXN1bHRzIiwicmVzdWx0cyIsImZvcm1hdHRlZFJlc3VsdCIsIlByaWNlIiwic3VwYWJhc2UiLCJzaXRlTmFtZSIsImZpbmFsU2l0ZUlkIiwibG9nIiwiZGF0YSIsInNpdGUiLCJzaXRlRXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJzaW5nbGUiLCJzaXRlX25hbWUiLCJkb21haW5EYXRhIiwiZG9tYWluX25hbWUiLCJ1c2VyX2lkIiwic2l0ZV9pZCIsInN0cmlwZV9zZXNzaW9uX2lkIiwiY25hbWVfdGFyZ2V0IiwiZXhpc3RpbmdEb21haW4iLCJkb21haW5SZWNvcmQiLCJ1cGRhdGVFcnJvciIsInVwZGF0ZSIsImlkIiwiaW5zZXJ0RXJyb3IiLCJpbnNlcnQiLCJyZWdpc3RyYXRpb25SZXN1bHQiLCJyZWdpc3RyYXRpb25EYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiZXhwaXJ5RGF0ZSIsInNldEZ1bGxZZWFyIiwiZ2V0RnVsbFllYXIiLCJyZWdpc3RyYXRpb25fZGF0ZSIsImV4cGlyeV9kYXRlIiwibmFtZWNoZWFwX29yZGVyX2lkIiwibmFtZWNoZWFwX3RyYW5zYWN0aW9uX2lkIiwicmVnRXJyb3IiLCJjbmFtZVJlc3VsdCIsInNpdGVOYW1lVXBkYXRlZCIsImRuc19jb25maWd1cmVkIiwiY25hbWVFcnIiLCJjbmFtZUVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/namecheap/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();