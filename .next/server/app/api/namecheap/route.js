/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/namecheap/route";
exports.ids = ["app/api/namecheap/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/namecheap/route.ts */ \"(rsc)/./src/app/api/namecheap/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/namecheap/route\",\n        pathname: \"/api/namecheap\",\n        filename: \"route\",\n        bundlePath: \"app/api/namecheap/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/namecheap/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_namecheap_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/namecheap/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/namecheap/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/namecheap/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/app/api/namecheap/route.ts\n\n\nconst COMMON_TLDS = [\n    {\n        tld: \"com\",\n        price: 12.98\n    },\n    {\n        tld: \"net\",\n        price: 14.98\n    },\n    {\n        tld: \"org\",\n        price: 14.98\n    },\n    {\n        tld: \"info\",\n        price: 18.98\n    },\n    {\n        tld: \"biz\",\n        price: 18.98\n    },\n    {\n        tld: \"com.au\",\n        price: 16.50\n    },\n    {\n        tld: \"net.au\",\n        price: 16.50\n    },\n    {\n        tld: \"org.au\",\n        price: 16.50\n    },\n    {\n        tld: \"co\",\n        price: 32.98\n    },\n    {\n        tld: \"io\",\n        price: 59.98\n    }\n];\nconst USD_TO_AUD_RATE = 1.5;\n// ... (checkDomainAvailability, generateDomainVariations, getPriceForDomain functions are unchanged)\nasync function checkDomainAvailability(domainList) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    if (!apiUser || !apiKey || !username || !clientIp) {\n        throw new Error(\"Missing Namecheap API configuration\");\n    }\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.check\",\n        ClientIp: clientIp,\n        DomainList: domainList.join(\",\")\n    });\n    try {\n        const response = await fetch(`${baseUrl}?${params.toString()}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\"\n            }\n        });\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        const xmlText = await response.text();\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n        if (errorMatch) throw new Error(`Namecheap API error: ${errorMatch[1]}`);\n        const domainResults = [];\n        const domainRegex = /<DomainCheckResult[^>]*Domain=\"([^\"]*)\"[^>]*Available=\"([^\"]*)\"[^>]*IsPremiumName=\"([^\"]*)\"[^>]*(?:PremiumRegistrationPrice=\"([^\"]*)\")?[^>]*\\/>/g;\n        let match;\n        while((match = domainRegex.exec(xmlText)) !== null){\n            const [, domainName, available, isPremium, premiumPrice] = match;\n            const result = {\n                Domain: domainName,\n                Available: available === \"true\",\n                IsPremiumName: isPremium === \"true\"\n            };\n            if (result.IsPremiumName && premiumPrice) result.PremiumRegistrationPrice = parseFloat(premiumPrice);\n            domainResults.push(result);\n        }\n        return domainResults;\n    } catch (error) {\n        console.error(\"Namecheap API error:\", error);\n        throw error;\n    }\n}\nfunction generateDomainVariations(baseDomain) {\n    const domainName = baseDomain.replace(/\\.(com|net|org|info|biz|com\\.au|net\\.au|org\\.au|co|io)$/i, \"\");\n    return COMMON_TLDS.map(({ tld })=>`${domainName}.${tld}`);\n}\nfunction getPriceForDomain(domain, namecheapResult) {\n    if (namecheapResult.IsPremiumName && namecheapResult.PremiumRegistrationPrice) {\n        return namecheapResult.PremiumRegistrationPrice * USD_TO_AUD_RATE + 5;\n    }\n    const tld = domain.split(\".\").slice(1).join(\".\");\n    const tldInfo = COMMON_TLDS.find((t)=>t.tld === tld);\n    if (tldInfo) return tldInfo.price * USD_TO_AUD_RATE + 5;\n    return 20 * USD_TO_AUD_RATE + 5;\n}\n// ✨ NEW FUNCTION: To register a domain with Namecheap\nasync function registerDomain(domainName) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    // Registrant contact information sourced from environment variables\n    const registrant = {\n        FirstName: process.env.REGISTRANT_FIRST_NAME,\n        LastName: process.env.REGISTRANT_LAST_NAME,\n        Address1: process.env.REGISTRANT_ADDRESS1,\n        City: process.env.REGISTRANT_CITY,\n        StateProvince: process.env.REGISTRANT_STATE_PROVINCE,\n        PostalCode: process.env.REGISTRANT_POSTAL_CODE,\n        Country: process.env.REGISTRANT_COUNTRY,\n        Phone: process.env.REGISTRANT_PHONE,\n        EmailAddress: process.env.REGISTRANT_EMAIL\n    };\n    for (const [key, value] of Object.entries(registrant)){\n        if (!value) {\n            throw new Error(`Missing required registrant environment variable: REGISTRANT_${key.toUpperCase()}`);\n        }\n    }\n    // ✨ FIX: Correctly map the registrant fields to the required API parameters.\n    // The previous code used `...registrant` which resulted in sending `FirstName` instead of `RegistrantFirstName`.\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.create\",\n        ClientIp: clientIp,\n        DomainName: domainName,\n        Years: \"1\",\n        // Registrant contact details\n        RegistrantFirstName: registrant.FirstName,\n        RegistrantLastName: registrant.LastName,\n        RegistrantAddress1: registrant.Address1,\n        RegistrantCity: registrant.City,\n        RegistrantStateProvince: registrant.StateProvince,\n        RegistrantPostalCode: registrant.PostalCode,\n        RegistrantCountry: registrant.Country,\n        RegistrantPhone: registrant.Phone,\n        RegistrantEmailAddress: registrant.EmailAddress,\n        // Admin, Tech, and AuxBilling contacts (using the same details)\n        AdminFirstName: registrant.FirstName,\n        AdminLastName: registrant.LastName,\n        AdminAddress1: registrant.Address1,\n        AdminCity: registrant.City,\n        AdminStateProvince: registrant.StateProvince,\n        AdminPostalCode: registrant.PostalCode,\n        AdminCountry: registrant.Country,\n        AdminPhone: registrant.Phone,\n        AdminEmailAddress: registrant.EmailAddress,\n        TechFirstName: registrant.FirstName,\n        TechLastName: registrant.LastName,\n        TechAddress1: registrant.Address1,\n        TechCity: registrant.City,\n        TechStateProvince: registrant.StateProvince,\n        TechPostalCode: registrant.PostalCode,\n        TechCountry: registrant.Country,\n        TechPhone: registrant.Phone,\n        TechEmailAddress: registrant.EmailAddress,\n        AuxBillingFirstName: registrant.FirstName,\n        AuxBillingLastName: registrant.LastName,\n        AuxBillingAddress1: registrant.Address1,\n        AuxBillingCity: registrant.City,\n        AuxBillingStateProvince: registrant.StateProvince,\n        AuxBillingPostalCode: registrant.PostalCode,\n        AuxBillingCountry: registrant.Country,\n        AuxBillingPhone: registrant.Phone,\n        AuxBillingEmailAddress: registrant.EmailAddress\n    });\n    try {\n        const response = await fetch(`${baseUrl}?${params.toString()}`, {\n            method: \"POST\"\n        });\n        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);\n        const xmlText = await response.text();\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n        if (errorMatch) {\n            throw new Error(`Namecheap API Error: ${errorMatch[1]}`);\n        }\n        const successMatch = xmlText.match(/<DomainCreateResult[^>]*Registered=\"true\"[^>]*>/);\n        if (!successMatch) {\n            // Provide a more detailed error if registration is not explicitly confirmed\n            throw new Error(`Domain registration failed. The response from Namecheap did not confirm success. XML: ${xmlText}`);\n        }\n        const orderIdMatch = xmlText.match(/OrderID=\"(\\d+)\"/);\n        const transactionIdMatch = xmlText.match(/TransactionID=\"(\\d+)\"/);\n        return {\n            success: true,\n            message: `Domain ${domainName} registered successfully.`,\n            orderId: orderIdMatch ? orderIdMatch[1] : null,\n            transactionId: transactionIdMatch ? transactionIdMatch[1] : null\n        };\n    } catch (error) {\n        console.error(\"Namecheap registration error:\", error);\n        throw error;\n    }\n}\n// Helper to get Supabase client (service role)\nfunction getSupabaseServiceClient() {\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n}\n// Function to set CNAME record via Namecheap API\nasync function setCNAMERecord(domain, cnameValue) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    // Namecheap API for DNS setHosts\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.dns.setHosts\",\n        ClientIp: clientIp,\n        SLD: domain.split(\".\")[0],\n        TLD: domain.split(\".\").slice(1).join(\".\"),\n        \"HostName1\": \"@\",\n        \"RecordType1\": \"CNAME\",\n        \"Address1\": cnameValue,\n        \"TTL1\": \"1800\"\n    });\n    const response = await fetch(`${baseUrl}?${params.toString()}`, {\n        method: \"POST\"\n    });\n    const xmlText = await response.text();\n    const errorMatch = xmlText.match(/<Error[^>]*>([^<]*)<\\/Error>/);\n    if (errorMatch) {\n        throw new Error(`Namecheap DNS Error: ${errorMatch[1]}`);\n    }\n    const successMatch = xmlText.match(/<DomainDNSSetHostsResult[^>]*IsSuccess=\"true\"/);\n    if (!successMatch) {\n        throw new Error(`CNAME mapping failed. XML: ${xmlText}`);\n    }\n    return {\n        success: true,\n        message: `CNAME for ${domain} -> ${cnameValue} set successfully.`\n    };\n}\nasync function POST(request) {\n    try {\n        const { domain, action, siteId } = await request.json();\n        if (!domain) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (action === \"check\") {\n            const domainVariations = generateDomainVariations(domain);\n            const namecheapResults = await checkDomainAvailability(domainVariations);\n            const results = namecheapResults.map((result)=>{\n                const formattedResult = {\n                    Domain: result.Domain,\n                    Available: result.Available,\n                    IsPremiumName: result.IsPremiumName\n                };\n                if (result.Available) {\n                    formattedResult.Price = getPriceForDomain(result.Domain, result);\n                }\n                return formattedResult;\n            });\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                results\n            });\n        }\n        if (action === \"register\") {\n            if (!siteId) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"siteId is required for registration\"\n                }, {\n                    status: 400\n                });\n            }\n            const supabase = getSupabaseServiceClient();\n            // Get user ID from the request (should be passed from the completion page)\n            const { userId, stripeSessionId } = await request.json();\n            if (!userId) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"userId is required for registration\"\n                }, {\n                    status: 400\n                });\n            }\n            // Fetch site_name from Supabase\n            console.log(\"[Namecheap API] Fetching site_name for siteId:\", siteId);\n            const { data: site, error: siteError } = await supabase.from(\"user-websites\").select(\"site_name\").eq(\"id\", siteId).single();\n            if (siteError || !site) {\n                console.error(\"[Namecheap API] Could not fetch site_name for siteId:\", siteId, siteError);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: \"Could not fetch site_name for siteId\"\n                }, {\n                    status: 400\n                });\n            }\n            const siteName = site.site_name;\n            console.log(\"[Namecheap API] site_name fetched:\", siteName);\n            // Create or update domain record in database\n            const domainData = {\n                domain_name: domain,\n                user_id: userId,\n                site_id: siteId,\n                status: \"pending\",\n                stripe_session_id: stripeSessionId,\n                cname_target: siteName\n            };\n            // Check if domain record already exists\n            const { data: existingDomain } = await supabase.from(\"domains\").select(\"id\").eq(\"domain_name\", domain).eq(\"user_id\", userId).single();\n            let domainRecord;\n            if (existingDomain) {\n                // Update existing domain record\n                const { data, error: updateError } = await supabase.from(\"domains\").update({\n                    ...domainData,\n                    status: \"pending\"\n                }).eq(\"id\", existingDomain.id).select().single();\n                if (updateError) {\n                    console.error(\"[Namecheap API] Failed to update domain record:\", updateError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Failed to update domain record\"\n                    }, {\n                        status: 500\n                    });\n                }\n                domainRecord = data;\n            } else {\n                // Create new domain record\n                const { data, error: insertError } = await supabase.from(\"domains\").insert(domainData).select().single();\n                if (insertError) {\n                    console.error(\"[Namecheap API] Failed to create domain record:\", insertError);\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        error: \"Failed to create domain record\"\n                    }, {\n                        status: 500\n                    });\n                }\n                domainRecord = data;\n            }\n            // Register domain with Namecheap\n            console.log(\"[Namecheap API] Registering domain:\", domain);\n            let registrationResult;\n            try {\n                registrationResult = await registerDomain(domain);\n                console.log(\"[Namecheap API] Registration result:\", registrationResult);\n                // Update domain record with registration details\n                const registrationDate = new Date().toISOString();\n                const expiryDate = new Date();\n                expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 1 year from now\n                await supabase.from(\"domains\").update({\n                    status: \"registered\",\n                    registration_date: registrationDate,\n                    expiry_date: expiryDate.toISOString(),\n                    namecheap_order_id: registrationResult.orderId,\n                    namecheap_transaction_id: registrationResult.transactionId\n                }).eq(\"id\", domainRecord.id);\n            } catch (regError) {\n                console.error(\"[Namecheap API] Registration failed:\", regError);\n                // Update domain record to failed status\n                await supabase.from(\"domains\").update({\n                    status: \"failed\"\n                }).eq(\"id\", domainRecord.id);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    error: regError instanceof Error ? regError.message : \"Domain registration failed\"\n                }, {\n                    status: 500\n                });\n            }\n            // Set CNAME record\n            let cnameResult = null;\n            try {\n                console.log(\"[Namecheap API] Setting CNAME for domain:\", domain, \"to:\", siteName);\n                cnameResult = await setCNAMERecord(domain, siteName);\n                console.log(\"[Namecheap API] CNAME mapping result:\", cnameResult);\n                // Update domain record with DNS configuration\n                await supabase.from(\"domains\").update({\n                    dns_configured: true,\n                    status: \"active\"\n                }).eq(\"id\", domainRecord.id);\n            } catch (cnameErr) {\n                console.error(\"[Namecheap API] CNAME mapping error:\", cnameErr);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    ...registrationResult,\n                    cnameError: cnameErr instanceof Error ? cnameErr.message : cnameErr\n                }, {\n                    status: 500\n                });\n            }\n            // Update site_name to the new domain\n            console.log(\"[Namecheap API] Updating site_name for siteId:\", siteId, \"to:\", domain);\n            const { error: updateError } = await supabase.from(\"user-websites\").update({\n                site_name: domain\n            }).eq(\"id\", siteId);\n            if (updateError) {\n                console.error(\"[Namecheap API] Failed to update site_name:\", updateError);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    ...registrationResult,\n                    cnameResult,\n                    updateError: updateError.message\n                }, {\n                    status: 500\n                });\n            }\n            console.log(\"[Namecheap API] site_name updated successfully.\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                ...registrationResult,\n                cnameResult,\n                siteNameUpdated: true,\n                domainRecord\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Invalid action\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: error instanceof Error ? error.message : \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/namecheap/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnamecheap%2Froute&page=%2Fapi%2Fnamecheap%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnamecheap%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();