/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/create-checkout-session/route";
exports.ids = ["app/api/create-checkout-session/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/create-checkout-session/route.ts */ \"(rsc)/./src/app/api/create-checkout-session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/create-checkout-session/route\",\n        pathname: \"/api/create-checkout-session\",\n        filename: \"route\",\n        bundlePath: \"app/api/create-checkout-session/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/create-checkout-session/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_create_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/create-checkout-session/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/create-checkout-session/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/create-checkout-session/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _lib_stripe_plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stripe-plans */ \"(rsc)/./src/lib/stripe-plans.ts\");\n/* harmony import */ var _lib_stripe_customer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-customer */ \"(rsc)/./src/lib/stripe-customer.ts\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// /api/create-checkout-session\n\n\n\n\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY);\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVybWFheG5veWNrZXpianRlZ21xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMzI2NDgsImV4cCI6MjA2NjkwODY0OH0.ngJdxCneL3KSN-PxlSybKSplflElwH8PKD_J4-_gilI\");\nasync function POST(request) {\n    try {\n        const { planId, isYearly, siteId } = await request.json();\n        // Get the authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return new Response(JSON.stringify({\n                error: \"Not authenticated\"\n            }), {\n                status: 401,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n        // Get the user from the JWT\n        const { data: { user }, error: authError } = await supabase.auth.getUser(token);\n        if (authError || !user) {\n            return new Response(JSON.stringify({\n                error: \"Invalid user\"\n            }), {\n                status: 401,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n        // Get or create Stripe customer for this user\n        const stripeCustomerId = await (0,_lib_stripe_customer__WEBPACK_IMPORTED_MODULE_2__.getOrCreateStripeCustomer)(user.id, user.email);\n        const baseUrl = \"http://localhost:3000\" || 0;\n        const session = await stripe.checkout.sessions.create({\n            mode: \"subscription\",\n            payment_method_types: [\n                \"card\"\n            ],\n            customer: stripeCustomerId,\n            line_items: [\n                {\n                    price: (0,_lib_stripe_plans__WEBPACK_IMPORTED_MODULE_1__.getPriceId)(planId, isYearly),\n                    quantity: 1\n                }\n            ],\n            metadata: {\n                planId,\n                isYearly: String(isYearly),\n                siteId: siteId || \"\",\n                userId: user.id\n            },\n            success_url: `${baseUrl}/dashboard?siteId=${siteId || \"\"}&postCheckout=1`,\n            cancel_url: `${baseUrl}/payments?siteId=${siteId || \"\"}`\n        });\n        return Response.json({\n            url: session.url\n        });\n    } catch (error) {\n        console.error(\"Error creating checkout session:\", error);\n        return new Response(JSON.stringify({\n            error: \"Failed to create checkout session\"\n        }), {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/create-checkout-session/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-customer.ts":
/*!************************************!*\
  !*** ./src/lib/stripe-customer.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOrCreateStripeCustomer: () => (/* binding */ getOrCreateStripeCustomer),\n/* harmony export */   getStripeCustomerIdFromProfile: () => (/* binding */ getStripeCustomerIdFromProfile),\n/* harmony export */   updateStripeCustomerFromProfile: () => (/* binding */ updateStripeCustomerFromProfile)\n/* harmony export */ });\n/* harmony import */ var _stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe */ \"(rsc)/./src/lib/stripe.ts\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Create Supabase client with service role for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n/**\n * Get or create a Stripe customer for a Supabase user\n */ async function getOrCreateStripeCustomer(userId, userEmail) {\n    try {\n        // First, check if user already has a Stripe customer ID in profiles table\n        const { data: profile, error: profileError } = await supabaseAdmin.from(\"profiles\").select(\"stripe_customer_id, first_name, last_name, phone, address1, city, state_province, postal_code, country\").eq(\"email\", userEmail).single();\n        if (profileError && profileError.code !== \"PGRST116\") {\n            console.error(\"Error fetching profile:\", profileError);\n            throw new Error(\"Failed to fetch user profile\");\n        }\n        // If user already has a Stripe customer ID, verify it exists in Stripe\n        if (profile?.stripe_customer_id) {\n            try {\n                const customer = await _stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.customers.retrieve(profile.stripe_customer_id);\n                if (!customer.deleted) {\n                    return profile.stripe_customer_id;\n                }\n            } catch (error) {\n                console.warn(\"Stripe customer not found, creating new one:\", error);\n            }\n        }\n        // Create new Stripe customer\n        const customerData = {\n            email: userEmail\n        };\n        // Add name if available\n        if (profile?.first_name || profile?.last_name) {\n            customerData.name = `${profile.first_name || \"\"} ${profile.last_name || \"\"}`.trim();\n        }\n        // Add phone if available\n        if (profile?.phone) {\n            customerData.phone = profile.phone;\n        }\n        // Add address if available\n        if (profile?.address1 || profile?.city || profile?.state_province || profile?.postal_code || profile?.country) {\n            customerData.address = {\n                line1: profile.address1 || undefined,\n                city: profile.city || undefined,\n                state: profile.state_province || undefined,\n                postal_code: profile.postal_code || undefined,\n                country: profile.country || undefined\n            };\n        }\n        const customer = await _stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.customers.create(customerData);\n        // Update the profiles table with the new Stripe customer ID\n        const { error: updateError } = await supabaseAdmin.from(\"profiles\").update({\n            stripe_customer_id: customer.id\n        }).eq(\"email\", userEmail);\n        if (updateError) {\n            console.error(\"Error updating profile with Stripe customer ID:\", updateError);\n        // Don't throw here as the customer was created successfully\n        }\n        return customer.id;\n    } catch (error) {\n        console.error(\"Error in getOrCreateStripeCustomer:\", error);\n        throw new Error(\"Failed to get or create Stripe customer\");\n    }\n}\n/**\n * Get Stripe customer ID from user profile\n */ async function getStripeCustomerIdFromProfile(userEmail) {\n    try {\n        const { data: profile, error } = await supabaseAdmin.from(\"profiles\").select(\"stripe_customer_id\").eq(\"email\", userEmail).single();\n        if (error) {\n            console.error(\"Error fetching profile:\", error);\n            return null;\n        }\n        return profile?.stripe_customer_id || null;\n    } catch (error) {\n        console.error(\"Error in getStripeCustomerIdFromProfile:\", error);\n        return null;\n    }\n}\n/**\n * Update Stripe customer with latest profile information\n */ async function updateStripeCustomerFromProfile(stripeCustomerId, userEmail) {\n    try {\n        const { data: profile, error } = await supabaseAdmin.from(\"profiles\").select(\"first_name, last_name, phone, address1, city, state_province, postal_code, country\").eq(\"email\", userEmail).single();\n        if (error) {\n            console.error(\"Error fetching profile for update:\", error);\n            return;\n        }\n        const updateData = {};\n        // Update name if available\n        if (profile?.first_name || profile?.last_name) {\n            updateData.name = `${profile.first_name || \"\"} ${profile.last_name || \"\"}`.trim();\n        }\n        // Update phone if available\n        if (profile?.phone) {\n            updateData.phone = profile.phone;\n        }\n        // Update address if available\n        if (profile?.address1 || profile?.city || profile?.state_province || profile?.postal_code || profile?.country) {\n            updateData.address = {\n                line1: profile.address1 || undefined,\n                city: profile.city || undefined,\n                state: profile.state_province || undefined,\n                postal_code: profile.postal_code || undefined,\n                country: profile.country || undefined\n            };\n        }\n        if (Object.keys(updateData).length > 0) {\n            await _stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.customers.update(stripeCustomerId, updateData);\n        }\n    } catch (error) {\n        console.error(\"Error updating Stripe customer:\", error);\n    // Don't throw as this is not critical\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-customer.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe-plans.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPriceId: () => (/* binding */ getPriceId),\n/* harmony export */   plans: () => (/* binding */ plans)\n/* harmony export */ });\nconst plans = {\n    starter: {\n        monthly: \"price_1Rh6gZHKmibvltr1B4VM6bW3\",\n        yearly: \"price_1Rl1IwHKmibvltr1UqjhoNk8\"\n    },\n    pro: {\n        monthly: \"price_1PLaO5HKmibvltr1AFSWYvYl\",\n        yearly: \"price_1PLaO5HKmibvltr1Q22gVz8P\"\n    },\n    turbo: {\n        monthly: \"price_1PLaOKHKmibvltr18ySjBqDB\",\n        yearly: \"price_1PLaOKHKmibvltr1yC4hTRs8\"\n    }\n};\nconst getPriceId = (planId, isYearly)=>{\n    const plan = plans[planId];\n    if (!plan) {\n        throw new Error(`Plan not found: ${planId}`);\n    }\n    return isYearly ? plan.yearly : plan.monthly;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS1wbGFucy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPLE1BQU1BLFFBQVE7SUFDbkJDLFNBQVM7UUFDUEMsU0FBUztRQUNUQyxRQUFRO0lBQ1Y7SUFDQUMsS0FBSztRQUNIRixTQUFTO1FBQ1RDLFFBQVE7SUFDVjtJQUNBRSxPQUFPO1FBQ0xILFNBQVM7UUFDVEMsUUFBUTtJQUNWO0FBQ0YsRUFBRTtBQUVLLE1BQU1HLGFBQWEsQ0FBQ0MsUUFBZ0JDO0lBQ3pDLE1BQU1DLE9BQU9ULEtBQUssQ0FBQ08sT0FBTztJQUMxQixJQUFJLENBQUNFLE1BQU07UUFDVCxNQUFNLElBQUlDLE1BQU0sQ0FBQyxnQkFBZ0IsRUFBRUgsT0FBTyxDQUFDO0lBQzdDO0lBQ0EsT0FBT0MsV0FBV0MsS0FBS04sTUFBTSxHQUFHTSxLQUFLUCxPQUFPO0FBQzlDLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vc3JjL2xpYi9zdHJpcGUtcGxhbnMudHM/ODUwNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgcGxhbnMgPSB7XG4gIHN0YXJ0ZXI6IHtcbiAgICBtb250aGx5OiAncHJpY2VfMVJoNmdaSEttaWJ2bHRyMUI0Vk02YlczJyxcbiAgICB5ZWFybHk6ICdwcmljZV8xUmwxSXdIS21pYnZsdHIxVXFqaG9OazgnLFxuICB9LFxuICBwcm86IHtcbiAgICBtb250aGx5OiAncHJpY2VfMVBMYU81SEttaWJ2bHRyMUFGU1dZdllsJyxcbiAgICB5ZWFybHk6ICdwcmljZV8xUExhTzVIS21pYnZsdHIxUTIyZ1Z6OFAnLFxuICB9LFxuICB0dXJibzoge1xuICAgIG1vbnRobHk6ICdwcmljZV8xUExhT0tIS21pYnZsdHIxOHlTakJxREInLFxuICAgIHllYXJseTogJ3ByaWNlXzFQTGFPS0hLbWlidmx0cjF5QzRoVFJzOCcsXG4gIH0sXG59O1xuXG5leHBvcnQgY29uc3QgZ2V0UHJpY2VJZCA9IChwbGFuSWQ6IHN0cmluZywgaXNZZWFybHk6IGJvb2xlYW4pOiBzdHJpbmcgPT4ge1xuICBjb25zdCBwbGFuID0gcGxhbnNbcGxhbklkXTtcbiAgaWYgKCFwbGFuKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBQbGFuIG5vdCBmb3VuZDogJHtwbGFuSWR9YCk7XG4gIH1cbiAgcmV0dXJuIGlzWWVhcmx5ID8gcGxhbi55ZWFybHkgOiBwbGFuLm1vbnRobHk7XG59O1xuIl0sIm5hbWVzIjpbInBsYW5zIiwic3RhcnRlciIsIm1vbnRobHkiLCJ5ZWFybHkiLCJwcm8iLCJ0dXJibyIsImdldFByaWNlSWQiLCJwbGFuSWQiLCJpc1llYXJseSIsInBsYW4iLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe.ts":
/*!***************************!*\
  !*** ./src/lib/stripe.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\nif (!process.env.STRIPE_SECRET_KEY) {\n    throw new Error(\"STRIPE_SECRET_KEY is not set\");\n}\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2025-06-30.basil\",\n    typescript: true\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QjtBQUU1QixJQUFJLENBQUNDLFFBQVFDLEdBQUcsQ0FBQ0MsaUJBQWlCLEVBQUU7SUFDbEMsTUFBTSxJQUFJQyxNQUFNO0FBQ2xCO0FBRU8sTUFBTUMsU0FBUyxJQUFJTCw4Q0FBTUEsQ0FBQ0MsUUFBUUMsR0FBRyxDQUFDQyxpQkFBaUIsRUFBRTtJQUM5REcsWUFBWTtJQUNaQyxZQUFZO0FBQ2QsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvbGliL3N0cmlwZS50cz83OThhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTdHJpcGUgZnJvbSAnc3RyaXBlJztcblxuaWYgKCFwcm9jZXNzLmVudi5TVFJJUEVfU0VDUkVUX0tFWSkge1xuICB0aHJvdyBuZXcgRXJyb3IoJ1NUUklQRV9TRUNSRVRfS0VZIGlzIG5vdCBzZXQnKTtcbn1cblxuZXhwb3J0IGNvbnN0IHN0cmlwZSA9IG5ldyBTdHJpcGUocHJvY2Vzcy5lbnYuU1RSSVBFX1NFQ1JFVF9LRVksIHtcbiAgYXBpVmVyc2lvbjogJzIwMjUtMDYtMzAuYmFzaWwnLFxuICB0eXBlc2NyaXB0OiB0cnVlLFxufSk7XG4iXSwibmFtZXMiOlsiU3RyaXBlIiwicHJvY2VzcyIsImVudiIsIlNUUklQRV9TRUNSRVRfS0VZIiwiRXJyb3IiLCJzdHJpcGUiLCJhcGlWZXJzaW9uIiwidHlwZXNjcmlwdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcreate-checkout-session%2Froute&page=%2Fapi%2Fcreate-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcreate-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();