/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/domain-checkout-session/route";
exports.ids = ["app/api/domain-checkout-session/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-checkout-session%2Froute&page=%2Fapi%2Fdomain-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-checkout-session%2Froute&page=%2Fapi%2Fdomain-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_domain_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/domain-checkout-session/route.ts */ \"(rsc)/./src/app/api/domain-checkout-session/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/domain-checkout-session/route\",\n        pathname: \"/api/domain-checkout-session\",\n        filename: \"route\",\n        bundlePath: \"app/api/domain-checkout-session/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/domain-checkout-session/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_domain_checkout_session_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/domain-checkout-session/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-checkout-session%2Froute&page=%2Fapi%2Fdomain-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/domain-checkout-session/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/domain-checkout-session/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stripe */ \"(rsc)/./src/lib/stripe.ts\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n\n// Initialize Supabase client with service role key for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function POST(request) {\n    try {\n        const { domain, price, siteId } = await request.json();\n        if (!domain || !price) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the authorization header to identify the user\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the user from the JWT\n        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);\n        if (authError || !user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid user\"\n            }, {\n                status: 401\n            });\n        }\n        const baseUrl = \"http://localhost:3000\" || 0;\n        const session = await _lib_stripe__WEBPACK_IMPORTED_MODULE_1__.stripe.checkout.sessions.create({\n            mode: \"payment\",\n            payment_method_types: [\n                \"card\"\n            ],\n            line_items: [\n                {\n                    price_data: {\n                        currency: \"aud\",\n                        product_data: {\n                            name: `Domain Registration: ${domain}`,\n                            description: `Register domain ${domain} for 1 year`\n                        },\n                        unit_amount: Math.round(price * 100)\n                    },\n                    quantity: 1\n                }\n            ],\n            metadata: {\n                domain,\n                siteId: siteId || \"pending\",\n                userId: user.id,\n                userEmail: user.email\n            },\n            success_url: `${baseUrl}/dashboard/domain/complete?session_id={CHECKOUT_SESSION_ID}&domain=${encodeURIComponent(domain)}&siteId=${encodeURIComponent(siteId || \"pending\")}`,\n            cancel_url: `${baseUrl}/dashboard/domain`\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            url: session.url\n        });\n    } catch (error) {\n        console.error(\"Error creating domain checkout session:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to create domain checkout session\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/domain-checkout-session/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe.ts":
/*!***************************!*\
  !*** ./src/lib/stripe.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n\nif (!process.env.STRIPE_SECRET_KEY) {\n    throw new Error(\"STRIPE_SECRET_KEY is not set\");\n}\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2025-06-30.basil\",\n    typescript: true\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QjtBQUU1QixJQUFJLENBQUNDLFFBQVFDLEdBQUcsQ0FBQ0MsaUJBQWlCLEVBQUU7SUFDbEMsTUFBTSxJQUFJQyxNQUFNO0FBQ2xCO0FBRU8sTUFBTUMsU0FBUyxJQUFJTCw4Q0FBTUEsQ0FBQ0MsUUFBUUMsR0FBRyxDQUFDQyxpQkFBaUIsRUFBRTtJQUM5REcsWUFBWTtJQUNaQyxZQUFZO0FBQ2QsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvbGliL3N0cmlwZS50cz83OThhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBTdHJpcGUgZnJvbSAnc3RyaXBlJztcblxuaWYgKCFwcm9jZXNzLmVudi5TVFJJUEVfU0VDUkVUX0tFWSkge1xuICB0aHJvdyBuZXcgRXJyb3IoJ1NUUklQRV9TRUNSRVRfS0VZIGlzIG5vdCBzZXQnKTtcbn1cblxuZXhwb3J0IGNvbnN0IHN0cmlwZSA9IG5ldyBTdHJpcGUocHJvY2Vzcy5lbnYuU1RSSVBFX1NFQ1JFVF9LRVksIHtcbiAgYXBpVmVyc2lvbjogJzIwMjUtMDYtMzAuYmFzaWwnLFxuICB0eXBlc2NyaXB0OiB0cnVlLFxufSk7XG4iXSwibmFtZXMiOlsiU3RyaXBlIiwicHJvY2VzcyIsImVudiIsIlNUUklQRV9TRUNSRVRfS0VZIiwiRXJyb3IiLCJzdHJpcGUiLCJhcGlWZXJzaW9uIiwidHlwZXNjcmlwdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-checkout-session%2Froute&page=%2Fapi%2Fdomain-checkout-session%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-checkout-session%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();