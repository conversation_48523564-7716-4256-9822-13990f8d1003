/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/domain-mapping/route";
exports.ids = ["app/api/domain-mapping/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-mapping%2Froute&page=%2Fapi%2Fdomain-mapping%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-mapping%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-mapping%2Froute&page=%2Fapi%2Fdomain-mapping%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-mapping%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_domain_mapping_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/domain-mapping/route.ts */ \"(rsc)/./src/app/api/domain-mapping/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/domain-mapping/route\",\n        pathname: \"/api/domain-mapping\",\n        filename: \"route\",\n        bundlePath: \"app/api/domain-mapping/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/domain-mapping/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_domain_mapping_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/domain-mapping/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-mapping%2Froute&page=%2Fapi%2Fdomain-mapping%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-mapping%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/domain-mapping/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/domain-mapping/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Initialize Supabase client with service role key for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n// Function to set CNAME record via Namecheap API\nasync function setCNAMERecord(domain, cnameValue) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.dns.setHosts\",\n        ClientIp: clientIp,\n        SLD: domain.split(\".\")[0],\n        TLD: domain.split(\".\").slice(1).join(\".\"),\n        HostName1: \"@\",\n        RecordType1: \"CNAME\",\n        Address1: cnameValue,\n        TTL1: \"1800\"\n    });\n    const response = await fetch(`${baseUrl}?${params.toString()}`);\n    const xmlText = await response.text();\n    console.log(\"[CNAME API] Response:\", xmlText);\n    if (xmlText.includes(\"<Status>OK</Status>\")) {\n        return {\n            success: true,\n            message: \"CNAME record set successfully\"\n        };\n    } else {\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]+)<\\/Error>/);\n        const errorMessage = errorMatch ? errorMatch[1] : \"Unknown error setting CNAME record\";\n        throw new Error(errorMessage);\n    }\n}\n// POST /api/domain-mapping - Map a domain to a site with CNAME setup\nasync function POST(request) {\n    try {\n        // Get the authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the user from the JWT\n        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);\n        if (authError || !user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid user\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { domainName, siteId } = body;\n        if (!domainName || !siteId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain name and site ID are required\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"[Domain Mapping] Starting mapping process:\", {\n            domainName,\n            siteId,\n            userId: user.id\n        });\n        // 1. Fetch site information\n        const { data: site, error: siteError } = await supabaseAdmin.from(\"user-websites\").select(\"site_name, id\").eq(\"id\", siteId).eq(\"user_id\", user.id) // Ensure user owns the site\n        .single();\n        if (siteError || !site) {\n            console.error(\"[Domain Mapping] Site not found:\", siteError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Site not found or access denied\"\n            }, {\n                status: 404\n            });\n        }\n        const originalSiteName = site.site_name;\n        console.log(\"[Domain Mapping] Found site:\", originalSiteName);\n        // 2. Verify domain ownership\n        const { data: domain, error: domainError } = await supabaseAdmin.from(\"domains\").select(\"id, status\").eq(\"domain_name\", domainName).eq(\"user_id\", user.id).single();\n        if (domainError || !domain) {\n            console.error(\"[Domain Mapping] Domain not found:\", domainError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain not found or access denied\"\n            }, {\n                status: 404\n            });\n        }\n        console.log(\"[Domain Mapping] Found domain:\", domain);\n        // 3. Set up CNAME record\n        console.log(\"[Domain Mapping] Setting CNAME record:\", domainName, \"->\", originalSiteName);\n        let cnameResult;\n        try {\n            cnameResult = await setCNAMERecord(domainName, originalSiteName);\n            console.log(\"[Domain Mapping] CNAME setup successful:\", cnameResult);\n        } catch (cnameError) {\n            console.error(\"[Domain Mapping] CNAME setup failed:\", cnameError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Failed to configure DNS: ${cnameError instanceof Error ? cnameError.message : cnameError}`\n            }, {\n                status: 500\n            });\n        }\n        // 4. Update domain record\n        const { error: domainUpdateError } = await supabaseAdmin.from(\"domains\").update({\n            site_id: siteId,\n            dns_configured: true,\n            status: \"active\",\n            cname_target: originalSiteName,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", domain.id);\n        if (domainUpdateError) {\n            console.error(\"[Domain Mapping] Failed to update domain record:\", domainUpdateError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to update domain record\"\n            }, {\n                status: 500\n            });\n        }\n        // 5. Update site name to the new domain\n        const { error: siteUpdateError } = await supabaseAdmin.from(\"user-websites\").update({\n            site_name: domainName,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", siteId);\n        if (siteUpdateError) {\n            console.error(\"[Domain Mapping] Failed to update site name:\", siteUpdateError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to update site name\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"[Domain Mapping] Mapping completed successfully\");\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Domain mapped successfully\",\n            domain: domainName,\n            site: {\n                id: siteId,\n                oldName: originalSiteName,\n                newName: domainName\n            },\n            cnameResult\n        });\n    } catch (error) {\n        console.error(\"[Domain Mapping] Unexpected error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/domain-mapping/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-mapping%2Froute&page=%2Fapi%2Fdomain-mapping%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-mapping%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();