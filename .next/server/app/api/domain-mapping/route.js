/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/domain-mapping/route";
exports.ids = ["app/api/domain-mapping/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-mapping%2Froute&page=%2Fapi%2Fdomain-mapping%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-mapping%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-mapping%2Froute&page=%2Fapi%2Fdomain-mapping%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-mapping%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_domain_mapping_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/domain-mapping/route.ts */ \"(rsc)/./src/app/api/domain-mapping/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/domain-mapping/route\",\n        pathname: \"/api/domain-mapping\",\n        filename: \"route\",\n        bundlePath: \"app/api/domain-mapping/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/domain-mapping/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_domain_mapping_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/domain-mapping/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-mapping%2Froute&page=%2Fapi%2Fdomain-mapping%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-mapping%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/domain-mapping/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/domain-mapping/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Initialize Supabase client with service role key for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://ermaaxnoyckezbjtegmq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n// Function to set CNAME record via Namecheap API\nasync function setCNAMERecord(domain, cnameValue) {\n    const apiUser = process.env.NAMECHEAP_API_USER;\n    const apiKey = process.env.NAMECHEAP_API_KEY;\n    const username = process.env.NAMECHEAP_USERNAME;\n    const clientIp = process.env.NAMECHEAP_CLIENT_IP;\n    const sandbox = process.env.NAMECHEAP_SANDBOX === \"true\";\n    const baseUrl = sandbox ? \"https://api.sandbox.namecheap.com/xml.response\" : \"https://api.namecheap.com/xml.response\";\n    const params = new URLSearchParams({\n        ApiUser: apiUser,\n        ApiKey: apiKey,\n        UserName: username,\n        Command: \"namecheap.domains.dns.setHosts\",\n        ClientIp: clientIp,\n        SLD: domain.split(\".\")[0],\n        TLD: domain.split(\".\").slice(1).join(\".\"),\n        HostName1: \"@\",\n        RecordType1: \"CNAME\",\n        Address1: cnameValue,\n        TTL1: \"1800\"\n    });\n    const response = await fetch(`${baseUrl}?${params.toString()}`);\n    const xmlText = await response.text();\n    console.log(\"[CNAME API] Response:\", xmlText);\n    // Check for success - Namecheap uses Status=\"OK\" as an attribute\n    if (xmlText.includes('Status=\"OK\"') && xmlText.includes('IsSuccess=\"true\"')) {\n        return {\n            success: true,\n            message: \"CNAME record set successfully\"\n        };\n    } else {\n        // Look for error messages\n        const errorMatch = xmlText.match(/<Error[^>]*>([^<]+)<\\/Error>/);\n        if (errorMatch) {\n            throw new Error(errorMatch[1]);\n        }\n        // If no specific error found, check for general failure indicators\n        if (xmlText.includes('Status=\"ERROR\"') || xmlText.includes('IsSuccess=\"false\"')) {\n            throw new Error(\"CNAME record setup failed\");\n        }\n        // If we can't determine the status, assume failure for safety\n        throw new Error(\"Unable to determine CNAME setup status from API response\");\n    }\n}\n// POST /api/domain-mapping - Map a domain to a site with CNAME setup\nasync function POST(request) {\n    try {\n        // Get the authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        const token = authHeader?.replace(\"Bearer \", \"\");\n        if (!token) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // Get the user from the JWT\n        const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);\n        if (authError || !user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid user\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { domainName, siteId } = body;\n        if (!domainName || !siteId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain name and site ID are required\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"[Domain Mapping] Starting mapping process:\", {\n            domainName,\n            siteId,\n            userId: user.id\n        });\n        // 1. Fetch site information\n        const { data: site, error: siteError } = await supabaseAdmin.from(\"user-websites\").select(\"site_name, id\").eq(\"id\", siteId).eq(\"user_id\", user.id) // Ensure user owns the site\n        .single();\n        if (siteError || !site) {\n            console.error(\"[Domain Mapping] Site not found:\", siteError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Site not found or access denied\"\n            }, {\n                status: 404\n            });\n        }\n        const originalSiteName = site.site_name;\n        console.log(\"[Domain Mapping] Found site:\", originalSiteName);\n        // 2. Verify domain ownership\n        const { data: domain, error: domainError } = await supabaseAdmin.from(\"domains\").select(\"id, status\").eq(\"domain_name\", domainName).eq(\"user_id\", user.id).single();\n        if (domainError || !domain) {\n            console.error(\"[Domain Mapping] Domain not found:\", domainError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Domain not found or access denied\"\n            }, {\n                status: 404\n            });\n        }\n        console.log(\"[Domain Mapping] Found domain:\", domain);\n        // 3. Set up CNAME record\n        console.log(\"[Domain Mapping] Setting CNAME record:\", domainName, \"->\", originalSiteName);\n        let cnameResult;\n        try {\n            cnameResult = await setCNAMERecord(domainName, originalSiteName);\n            console.log(\"[Domain Mapping] CNAME setup successful:\", cnameResult);\n        } catch (cnameError) {\n            console.error(\"[Domain Mapping] CNAME setup failed:\", cnameError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: `Failed to configure DNS: ${cnameError instanceof Error ? cnameError.message : cnameError}`\n            }, {\n                status: 500\n            });\n        }\n        // 4. Update domain record\n        const { error: domainUpdateError } = await supabaseAdmin.from(\"domains\").update({\n            site_id: siteId,\n            dns_configured: true,\n            status: \"active\",\n            cname_target: originalSiteName,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", domain.id);\n        if (domainUpdateError) {\n            console.error(\"[Domain Mapping] Failed to update domain record:\", domainUpdateError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to update domain record\"\n            }, {\n                status: 500\n            });\n        }\n        // 5. Update site name to the new domain\n        const { error: siteUpdateError } = await supabaseAdmin.from(\"user-websites\").update({\n            site_name: domainName,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", siteId);\n        if (siteUpdateError) {\n            console.error(\"[Domain Mapping] Failed to update site name:\", siteUpdateError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to update site name\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"[Domain Mapping] Mapping completed successfully\");\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Domain mapped successfully\",\n            domain: domainName,\n            site: {\n                id: siteId,\n                oldName: originalSiteName,\n                newName: domainName\n            },\n            cnameResult\n        });\n    } catch (error) {\n        console.error(\"[Domain Mapping] Unexpected error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9kb21haW4tbWFwcGluZy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0Q7QUFDSDtBQUVyRCw4RUFBOEU7QUFDOUUsTUFBTUUsZ0JBQWdCRCxtRUFBWUEsQ0FDaENFLDBDQUFvQyxFQUNwQ0EsUUFBUUMsR0FBRyxDQUFDRSx5QkFBeUI7QUFHdkMsaURBQWlEO0FBQ2pELGVBQWVDLGVBQWVDLE1BQWMsRUFBRUMsVUFBa0I7SUFDOUQsTUFBTUMsVUFBVVAsUUFBUUMsR0FBRyxDQUFDTyxrQkFBa0I7SUFDOUMsTUFBTUMsU0FBU1QsUUFBUUMsR0FBRyxDQUFDUyxpQkFBaUI7SUFDNUMsTUFBTUMsV0FBV1gsUUFBUUMsR0FBRyxDQUFDVyxrQkFBa0I7SUFDL0MsTUFBTUMsV0FBV2IsUUFBUUMsR0FBRyxDQUFDYSxtQkFBbUI7SUFDaEQsTUFBTUMsVUFBVWYsUUFBUUMsR0FBRyxDQUFDZSxpQkFBaUIsS0FBSztJQUVsRCxNQUFNQyxVQUFVRixVQUNaLG1EQUNBO0lBRUosTUFBTUcsU0FBUyxJQUFJQyxnQkFBZ0I7UUFDakNDLFNBQVNiO1FBQ1RjLFFBQVFaO1FBQ1JhLFVBQVVYO1FBQ1ZZLFNBQVM7UUFDVEMsVUFBVVg7UUFDVlksS0FBS3BCLE9BQU9xQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFDekJDLEtBQUt0QixPQUFPcUIsS0FBSyxDQUFDLEtBQUtFLEtBQUssQ0FBQyxHQUFHQyxJQUFJLENBQUM7UUFDckNDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxVQUFVMUI7UUFDVjJCLE1BQU07SUFDUjtJQUVBLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxDQUFDLEVBQUVsQixRQUFRLENBQUMsRUFBRUMsT0FBT2tCLFFBQVEsR0FBRyxDQUFDO0lBQzlELE1BQU1DLFVBQVUsTUFBTUgsU0FBU0ksSUFBSTtJQUVuQ0MsUUFBUUMsR0FBRyxDQUFDLHlCQUF5Qkg7SUFFckMsaUVBQWlFO0lBQ2pFLElBQUlBLFFBQVFJLFFBQVEsQ0FBQyxrQkFBa0JKLFFBQVFJLFFBQVEsQ0FBQyxxQkFBcUI7UUFDM0UsT0FBTztZQUFFQyxTQUFTO1lBQU1DLFNBQVM7UUFBZ0M7SUFDbkUsT0FBTztRQUNMLDBCQUEwQjtRQUMxQixNQUFNQyxhQUFhUCxRQUFRUSxLQUFLLENBQUM7UUFDakMsSUFBSUQsWUFBWTtZQUNkLE1BQU0sSUFBSUUsTUFBTUYsVUFBVSxDQUFDLEVBQUU7UUFDL0I7UUFFQSxtRUFBbUU7UUFDbkUsSUFBSVAsUUFBUUksUUFBUSxDQUFDLHFCQUFxQkosUUFBUUksUUFBUSxDQUFDLHNCQUFzQjtZQUMvRSxNQUFNLElBQUlLLE1BQU07UUFDbEI7UUFFQSw4REFBOEQ7UUFDOUQsTUFBTSxJQUFJQSxNQUFNO0lBQ2xCO0FBQ0Y7QUFFQSxxRUFBcUU7QUFDOUQsZUFBZUMsS0FBS0MsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLCtCQUErQjtRQUMvQixNQUFNQyxhQUFhRCxRQUFRRSxPQUFPLENBQUNDLEdBQUcsQ0FBQztRQUN2QyxNQUFNQyxRQUFRSCxZQUFZSSxRQUFRLFdBQVc7UUFFN0MsSUFBSSxDQUFDRCxPQUFPO1lBQ1YsT0FBT3ZELGtGQUFZQSxDQUFDeUQsSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQW9CLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUN6RTtRQUVBLDRCQUE0QjtRQUM1QixNQUFNLEVBQUVDLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUVILE9BQU9JLFNBQVMsRUFBRSxHQUFHLE1BQU01RCxjQUFjNkQsSUFBSSxDQUFDQyxPQUFPLENBQUNUO1FBQzlFLElBQUlPLGFBQWEsQ0FBQ0QsTUFBTTtZQUN0QixPQUFPN0Qsa0ZBQVlBLENBQUN5RCxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBZSxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDcEU7UUFFQSxNQUFNTSxPQUFPLE1BQU1kLFFBQVFNLElBQUk7UUFDL0IsTUFBTSxFQUFFUyxVQUFVLEVBQUVDLE1BQU0sRUFBRSxHQUFHRjtRQUUvQixJQUFJLENBQUNDLGNBQWMsQ0FBQ0MsUUFBUTtZQUMxQixPQUFPbkUsa0ZBQVlBLENBQUN5RCxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBdUMsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQzVGO1FBRUFqQixRQUFRQyxHQUFHLENBQUMsOENBQThDO1lBQUV1QjtZQUFZQztZQUFRQyxRQUFRUCxLQUFLUSxFQUFFO1FBQUM7UUFFaEcsNEJBQTRCO1FBQzVCLE1BQU0sRUFBRVQsTUFBTVUsSUFBSSxFQUFFWixPQUFPYSxTQUFTLEVBQUUsR0FBRyxNQUFNckUsY0FDNUNzRSxJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQyxpQkFDUEMsRUFBRSxDQUFDLE1BQU1QLFFBQ1RPLEVBQUUsQ0FBQyxXQUFXYixLQUFLUSxFQUFFLEVBQUUsNEJBQTRCO1NBQ25ETSxNQUFNO1FBRVQsSUFBSUosYUFBYSxDQUFDRCxNQUFNO1lBQ3RCNUIsUUFBUWdCLEtBQUssQ0FBQyxvQ0FBb0NhO1lBQ2xELE9BQU92RSxrRkFBWUEsQ0FBQ3lELElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFrQyxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDdkY7UUFFQSxNQUFNaUIsbUJBQW1CTixLQUFLTyxTQUFTO1FBQ3ZDbkMsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ2lDO1FBRTVDLDZCQUE2QjtRQUM3QixNQUFNLEVBQUVoQixNQUFNcEQsTUFBTSxFQUFFa0QsT0FBT29CLFdBQVcsRUFBRSxHQUFHLE1BQU01RSxjQUNoRHNFLElBQUksQ0FBQyxXQUNMQyxNQUFNLENBQUMsY0FDUEMsRUFBRSxDQUFDLGVBQWVSLFlBQ2xCUSxFQUFFLENBQUMsV0FBV2IsS0FBS1EsRUFBRSxFQUNyQk0sTUFBTTtRQUVULElBQUlHLGVBQWUsQ0FBQ3RFLFFBQVE7WUFDMUJrQyxRQUFRZ0IsS0FBSyxDQUFDLHNDQUFzQ29CO1lBQ3BELE9BQU85RSxrRkFBWUEsQ0FBQ3lELElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFvQyxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDekY7UUFFQWpCLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0NuQztRQUU5Qyx5QkFBeUI7UUFDekJrQyxRQUFRQyxHQUFHLENBQUMsMENBQTBDdUIsWUFBWSxNQUFNVTtRQUN4RSxJQUFJRztRQUNKLElBQUk7WUFDRkEsY0FBYyxNQUFNeEUsZUFBZTJELFlBQVlVO1lBQy9DbEMsUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q29DO1FBQzFELEVBQUUsT0FBT0MsWUFBWTtZQUNuQnRDLFFBQVFnQixLQUFLLENBQUMsd0NBQXdDc0I7WUFDdEQsT0FBT2hGLGtGQUFZQSxDQUFDeUQsSUFBSSxDQUFDO2dCQUN2QkMsT0FBTyxDQUFDLHlCQUF5QixFQUFFc0Isc0JBQXNCL0IsUUFBUStCLFdBQVdsQyxPQUFPLEdBQUdrQyxXQUFXLENBQUM7WUFDcEcsR0FBRztnQkFBRXJCLFFBQVE7WUFBSTtRQUNuQjtRQUVBLDBCQUEwQjtRQUMxQixNQUFNLEVBQUVELE9BQU91QixpQkFBaUIsRUFBRSxHQUFHLE1BQU0vRSxjQUN4Q3NFLElBQUksQ0FBQyxXQUNMVSxNQUFNLENBQUM7WUFDTkMsU0FBU2hCO1lBQ1RpQixnQkFBZ0I7WUFDaEJ6QixRQUFRO1lBQ1IwQixjQUFjVDtZQUNkVSxZQUFZLElBQUlDLE9BQU9DLFdBQVc7UUFDcEMsR0FDQ2QsRUFBRSxDQUFDLE1BQU1sRSxPQUFPNkQsRUFBRTtRQUVyQixJQUFJWSxtQkFBbUI7WUFDckJ2QyxRQUFRZ0IsS0FBSyxDQUFDLG9EQUFvRHVCO1lBQ2xFLE9BQU9qRixrRkFBWUEsQ0FBQ3lELElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFpQyxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDdEY7UUFFQSx3Q0FBd0M7UUFDeEMsTUFBTSxFQUFFRCxPQUFPK0IsZUFBZSxFQUFFLEdBQUcsTUFBTXZGLGNBQ3RDc0UsSUFBSSxDQUFDLGlCQUNMVSxNQUFNLENBQUM7WUFDTkwsV0FBV1g7WUFDWG9CLFlBQVksSUFBSUMsT0FBT0MsV0FBVztRQUNwQyxHQUNDZCxFQUFFLENBQUMsTUFBTVA7UUFFWixJQUFJc0IsaUJBQWlCO1lBQ25CL0MsUUFBUWdCLEtBQUssQ0FBQyxnREFBZ0QrQjtZQUM5RCxPQUFPekYsa0ZBQVlBLENBQUN5RCxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBNkIsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ2xGO1FBRUFqQixRQUFRQyxHQUFHLENBQUM7UUFFWixPQUFPM0Msa0ZBQVlBLENBQUN5RCxJQUFJLENBQUM7WUFDdkJaLFNBQVM7WUFDVEMsU0FBUztZQUNUdEMsUUFBUTBEO1lBQ1JJLE1BQU07Z0JBQ0pELElBQUlGO2dCQUNKdUIsU0FBU2Q7Z0JBQ1RlLFNBQVN6QjtZQUNYO1lBQ0FhO1FBQ0Y7SUFFRixFQUFFLE9BQU9yQixPQUFZO1FBQ25CaEIsUUFBUWdCLEtBQUssQ0FBQyxzQ0FBc0NBO1FBQ3BELE9BQU8xRCxrRkFBWUEsQ0FBQ3lELElBQUksQ0FBQztZQUFFQyxPQUFPO1FBQXdCLEdBQUc7WUFBRUMsUUFBUTtRQUFJO0lBQzdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93b3JkcHJlc3MtYWktYXBwLy4vc3JjL2FwcC9hcGkvZG9tYWluLW1hcHBpbmcvcm91dGUudHM/MTAzNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJztcblxuLy8gSW5pdGlhbGl6ZSBTdXBhYmFzZSBjbGllbnQgd2l0aCBzZXJ2aWNlIHJvbGUga2V5IGZvciBzZXJ2ZXItc2lkZSBvcGVyYXRpb25zXG5jb25zdCBzdXBhYmFzZUFkbWluID0gY3JlYXRlQ2xpZW50KFxuICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIVxuKTtcblxuLy8gRnVuY3Rpb24gdG8gc2V0IENOQU1FIHJlY29yZCB2aWEgTmFtZWNoZWFwIEFQSVxuYXN5bmMgZnVuY3Rpb24gc2V0Q05BTUVSZWNvcmQoZG9tYWluOiBzdHJpbmcsIGNuYW1lVmFsdWU6IHN0cmluZykge1xuICBjb25zdCBhcGlVc2VyID0gcHJvY2Vzcy5lbnYuTkFNRUNIRUFQX0FQSV9VU0VSITtcbiAgY29uc3QgYXBpS2V5ID0gcHJvY2Vzcy5lbnYuTkFNRUNIRUFQX0FQSV9LRVkhO1xuICBjb25zdCB1c2VybmFtZSA9IHByb2Nlc3MuZW52Lk5BTUVDSEVBUF9VU0VSTkFNRSE7XG4gIGNvbnN0IGNsaWVudElwID0gcHJvY2Vzcy5lbnYuTkFNRUNIRUFQX0NMSUVOVF9JUCE7XG4gIGNvbnN0IHNhbmRib3ggPSBwcm9jZXNzLmVudi5OQU1FQ0hFQVBfU0FOREJPWCA9PT0gJ3RydWUnO1xuXG4gIGNvbnN0IGJhc2VVcmwgPSBzYW5kYm94IFxuICAgID8gJ2h0dHBzOi8vYXBpLnNhbmRib3gubmFtZWNoZWFwLmNvbS94bWwucmVzcG9uc2UnXG4gICAgOiAnaHR0cHM6Ly9hcGkubmFtZWNoZWFwLmNvbS94bWwucmVzcG9uc2UnO1xuXG4gIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoe1xuICAgIEFwaVVzZXI6IGFwaVVzZXIsXG4gICAgQXBpS2V5OiBhcGlLZXksXG4gICAgVXNlck5hbWU6IHVzZXJuYW1lLFxuICAgIENvbW1hbmQ6ICduYW1lY2hlYXAuZG9tYWlucy5kbnMuc2V0SG9zdHMnLFxuICAgIENsaWVudElwOiBjbGllbnRJcCxcbiAgICBTTEQ6IGRvbWFpbi5zcGxpdCgnLicpWzBdLFxuICAgIFRMRDogZG9tYWluLnNwbGl0KCcuJykuc2xpY2UoMSkuam9pbignLicpLFxuICAgIEhvc3ROYW1lMTogJ0AnLFxuICAgIFJlY29yZFR5cGUxOiAnQ05BTUUnLFxuICAgIEFkZHJlc3MxOiBjbmFtZVZhbHVlLFxuICAgIFRUTDE6ICcxODAwJ1xuICB9KTtcblxuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9PyR7cGFyYW1zLnRvU3RyaW5nKCl9YCk7XG4gIGNvbnN0IHhtbFRleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG4gIFxuICBjb25zb2xlLmxvZygnW0NOQU1FIEFQSV0gUmVzcG9uc2U6JywgeG1sVGV4dCk7XG5cbiAgLy8gQ2hlY2sgZm9yIHN1Y2Nlc3MgLSBOYW1lY2hlYXAgdXNlcyBTdGF0dXM9XCJPS1wiIGFzIGFuIGF0dHJpYnV0ZVxuICBpZiAoeG1sVGV4dC5pbmNsdWRlcygnU3RhdHVzPVwiT0tcIicpICYmIHhtbFRleHQuaW5jbHVkZXMoJ0lzU3VjY2Vzcz1cInRydWVcIicpKSB7XG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgbWVzc2FnZTogJ0NOQU1FIHJlY29yZCBzZXQgc3VjY2Vzc2Z1bGx5JyB9O1xuICB9IGVsc2Uge1xuICAgIC8vIExvb2sgZm9yIGVycm9yIG1lc3NhZ2VzXG4gICAgY29uc3QgZXJyb3JNYXRjaCA9IHhtbFRleHQubWF0Y2goLzxFcnJvcltePl0qPihbXjxdKyk8XFwvRXJyb3I+Lyk7XG4gICAgaWYgKGVycm9yTWF0Y2gpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1hdGNoWzFdKTtcbiAgICB9XG5cbiAgICAvLyBJZiBubyBzcGVjaWZpYyBlcnJvciBmb3VuZCwgY2hlY2sgZm9yIGdlbmVyYWwgZmFpbHVyZSBpbmRpY2F0b3JzXG4gICAgaWYgKHhtbFRleHQuaW5jbHVkZXMoJ1N0YXR1cz1cIkVSUk9SXCInKSB8fCB4bWxUZXh0LmluY2x1ZGVzKCdJc1N1Y2Nlc3M9XCJmYWxzZVwiJykpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQ05BTUUgcmVjb3JkIHNldHVwIGZhaWxlZCcpO1xuICAgIH1cblxuICAgIC8vIElmIHdlIGNhbid0IGRldGVybWluZSB0aGUgc3RhdHVzLCBhc3N1bWUgZmFpbHVyZSBmb3Igc2FmZXR5XG4gICAgdGhyb3cgbmV3IEVycm9yKCdVbmFibGUgdG8gZGV0ZXJtaW5lIENOQU1FIHNldHVwIHN0YXR1cyBmcm9tIEFQSSByZXNwb25zZScpO1xuICB9XG59XG5cbi8vIFBPU1QgL2FwaS9kb21haW4tbWFwcGluZyAtIE1hcCBhIGRvbWFpbiB0byBhIHNpdGUgd2l0aCBDTkFNRSBzZXR1cFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICAvLyBHZXQgdGhlIGF1dGhvcml6YXRpb24gaGVhZGVyXG4gICAgY29uc3QgYXV0aEhlYWRlciA9IHJlcXVlc3QuaGVhZGVycy5nZXQoJ2F1dGhvcml6YXRpb24nKTtcbiAgICBjb25zdCB0b2tlbiA9IGF1dGhIZWFkZXI/LnJlcGxhY2UoJ0JlYXJlciAnLCAnJyk7XG5cbiAgICBpZiAoIXRva2VuKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ05vdCBhdXRoZW50aWNhdGVkJyB9LCB7IHN0YXR1czogNDAxIH0pO1xuICAgIH1cblxuICAgIC8vIEdldCB0aGUgdXNlciBmcm9tIHRoZSBKV1RcbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9LCBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguZ2V0VXNlcih0b2tlbik7XG4gICAgaWYgKGF1dGhFcnJvciB8fCAhdXNlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdJbnZhbGlkIHVzZXInIH0sIHsgc3RhdHVzOiA0MDEgfSk7XG4gICAgfVxuXG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuICAgIGNvbnN0IHsgZG9tYWluTmFtZSwgc2l0ZUlkIH0gPSBib2R5O1xuXG4gICAgaWYgKCFkb21haW5OYW1lIHx8ICFzaXRlSWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnRG9tYWluIG5hbWUgYW5kIHNpdGUgSUQgYXJlIHJlcXVpcmVkJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdbRG9tYWluIE1hcHBpbmddIFN0YXJ0aW5nIG1hcHBpbmcgcHJvY2VzczonLCB7IGRvbWFpbk5hbWUsIHNpdGVJZCwgdXNlcklkOiB1c2VyLmlkIH0pO1xuXG4gICAgLy8gMS4gRmV0Y2ggc2l0ZSBpbmZvcm1hdGlvblxuICAgIGNvbnN0IHsgZGF0YTogc2l0ZSwgZXJyb3I6IHNpdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgLmZyb20oJ3VzZXItd2Vic2l0ZXMnKVxuICAgICAgLnNlbGVjdCgnc2l0ZV9uYW1lLCBpZCcpXG4gICAgICAuZXEoJ2lkJywgc2l0ZUlkKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZCkgLy8gRW5zdXJlIHVzZXIgb3ducyB0aGUgc2l0ZVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKHNpdGVFcnJvciB8fCAhc2l0ZSkge1xuICAgICAgY29uc29sZS5lcnJvcignW0RvbWFpbiBNYXBwaW5nXSBTaXRlIG5vdCBmb3VuZDonLCBzaXRlRXJyb3IpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdTaXRlIG5vdCBmb3VuZCBvciBhY2Nlc3MgZGVuaWVkJyB9LCB7IHN0YXR1czogNDA0IH0pO1xuICAgIH1cblxuICAgIGNvbnN0IG9yaWdpbmFsU2l0ZU5hbWUgPSBzaXRlLnNpdGVfbmFtZTtcbiAgICBjb25zb2xlLmxvZygnW0RvbWFpbiBNYXBwaW5nXSBGb3VuZCBzaXRlOicsIG9yaWdpbmFsU2l0ZU5hbWUpO1xuXG4gICAgLy8gMi4gVmVyaWZ5IGRvbWFpbiBvd25lcnNoaXBcbiAgICBjb25zdCB7IGRhdGE6IGRvbWFpbiwgZXJyb3I6IGRvbWFpbkVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAuZnJvbSgnZG9tYWlucycpXG4gICAgICAuc2VsZWN0KCdpZCwgc3RhdHVzJylcbiAgICAgIC5lcSgnZG9tYWluX25hbWUnLCBkb21haW5OYW1lKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChkb21haW5FcnJvciB8fCAhZG9tYWluKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdbRG9tYWluIE1hcHBpbmddIERvbWFpbiBub3QgZm91bmQ6JywgZG9tYWluRXJyb3IpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6ICdEb21haW4gbm90IGZvdW5kIG9yIGFjY2VzcyBkZW5pZWQnIH0sIHsgc3RhdHVzOiA0MDQgfSk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ1tEb21haW4gTWFwcGluZ10gRm91bmQgZG9tYWluOicsIGRvbWFpbik7XG5cbiAgICAvLyAzLiBTZXQgdXAgQ05BTUUgcmVjb3JkXG4gICAgY29uc29sZS5sb2coJ1tEb21haW4gTWFwcGluZ10gU2V0dGluZyBDTkFNRSByZWNvcmQ6JywgZG9tYWluTmFtZSwgJy0+Jywgb3JpZ2luYWxTaXRlTmFtZSk7XG4gICAgbGV0IGNuYW1lUmVzdWx0O1xuICAgIHRyeSB7XG4gICAgICBjbmFtZVJlc3VsdCA9IGF3YWl0IHNldENOQU1FUmVjb3JkKGRvbWFpbk5hbWUsIG9yaWdpbmFsU2l0ZU5hbWUpO1xuICAgICAgY29uc29sZS5sb2coJ1tEb21haW4gTWFwcGluZ10gQ05BTUUgc2V0dXAgc3VjY2Vzc2Z1bDonLCBjbmFtZVJlc3VsdCk7XG4gICAgfSBjYXRjaCAoY25hbWVFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW0RvbWFpbiBNYXBwaW5nXSBDTkFNRSBzZXR1cCBmYWlsZWQ6JywgY25hbWVFcnJvcik7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBcbiAgICAgICAgZXJyb3I6IGBGYWlsZWQgdG8gY29uZmlndXJlIEROUzogJHtjbmFtZUVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBjbmFtZUVycm9yLm1lc3NhZ2UgOiBjbmFtZUVycm9yfWAgXG4gICAgICB9LCB7IHN0YXR1czogNTAwIH0pO1xuICAgIH1cblxuICAgIC8vIDQuIFVwZGF0ZSBkb21haW4gcmVjb3JkXG4gICAgY29uc3QgeyBlcnJvcjogZG9tYWluVXBkYXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgIC5mcm9tKCdkb21haW5zJylcbiAgICAgIC51cGRhdGUoe1xuICAgICAgICBzaXRlX2lkOiBzaXRlSWQsXG4gICAgICAgIGRuc19jb25maWd1cmVkOiB0cnVlLFxuICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgICAgICBjbmFtZV90YXJnZXQ6IG9yaWdpbmFsU2l0ZU5hbWUsXG4gICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgfSlcbiAgICAgIC5lcSgnaWQnLCBkb21haW4uaWQpO1xuXG4gICAgaWYgKGRvbWFpblVwZGF0ZUVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdbRG9tYWluIE1hcHBpbmddIEZhaWxlZCB0byB1cGRhdGUgZG9tYWluIHJlY29yZDonLCBkb21haW5VcGRhdGVFcnJvcik7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0ZhaWxlZCB0byB1cGRhdGUgZG9tYWluIHJlY29yZCcgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgICB9XG5cbiAgICAvLyA1LiBVcGRhdGUgc2l0ZSBuYW1lIHRvIHRoZSBuZXcgZG9tYWluXG4gICAgY29uc3QgeyBlcnJvcjogc2l0ZVVwZGF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAuZnJvbSgndXNlci13ZWJzaXRlcycpXG4gICAgICAudXBkYXRlKHtcbiAgICAgICAgc2l0ZV9uYW1lOiBkb21haW5OYW1lLFxuICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pXG4gICAgICAuZXEoJ2lkJywgc2l0ZUlkKTtcblxuICAgIGlmIChzaXRlVXBkYXRlRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1tEb21haW4gTWFwcGluZ10gRmFpbGVkIHRvIHVwZGF0ZSBzaXRlIG5hbWU6Jywgc2l0ZVVwZGF0ZUVycm9yKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSBzaXRlIG5hbWUnIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ1tEb21haW4gTWFwcGluZ10gTWFwcGluZyBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICdEb21haW4gbWFwcGVkIHN1Y2Nlc3NmdWxseScsXG4gICAgICBkb21haW46IGRvbWFpbk5hbWUsXG4gICAgICBzaXRlOiB7XG4gICAgICAgIGlkOiBzaXRlSWQsXG4gICAgICAgIG9sZE5hbWU6IG9yaWdpbmFsU2l0ZU5hbWUsXG4gICAgICAgIG5ld05hbWU6IGRvbWFpbk5hbWVcbiAgICAgIH0sXG4gICAgICBjbmFtZVJlc3VsdFxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdbRG9tYWluIE1hcHBpbmddIFVuZXhwZWN0ZWQgZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyB9LCB7IHN0YXR1czogNTAwIH0pO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VBZG1pbiIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwic2V0Q05BTUVSZWNvcmQiLCJkb21haW4iLCJjbmFtZVZhbHVlIiwiYXBpVXNlciIsIk5BTUVDSEVBUF9BUElfVVNFUiIsImFwaUtleSIsIk5BTUVDSEVBUF9BUElfS0VZIiwidXNlcm5hbWUiLCJOQU1FQ0hFQVBfVVNFUk5BTUUiLCJjbGllbnRJcCIsIk5BTUVDSEVBUF9DTElFTlRfSVAiLCJzYW5kYm94IiwiTkFNRUNIRUFQX1NBTkRCT1giLCJiYXNlVXJsIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwiQXBpVXNlciIsIkFwaUtleSIsIlVzZXJOYW1lIiwiQ29tbWFuZCIsIkNsaWVudElwIiwiU0xEIiwic3BsaXQiLCJUTEQiLCJzbGljZSIsImpvaW4iLCJIb3N0TmFtZTEiLCJSZWNvcmRUeXBlMSIsIkFkZHJlc3MxIiwiVFRMMSIsInJlc3BvbnNlIiwiZmV0Y2giLCJ0b1N0cmluZyIsInhtbFRleHQiLCJ0ZXh0IiwiY29uc29sZSIsImxvZyIsImluY2x1ZGVzIiwic3VjY2VzcyIsIm1lc3NhZ2UiLCJlcnJvck1hdGNoIiwibWF0Y2giLCJFcnJvciIsIlBPU1QiLCJyZXF1ZXN0IiwiYXV0aEhlYWRlciIsImhlYWRlcnMiLCJnZXQiLCJ0b2tlbiIsInJlcGxhY2UiLCJqc29uIiwiZXJyb3IiLCJzdGF0dXMiLCJkYXRhIiwidXNlciIsImF1dGhFcnJvciIsImF1dGgiLCJnZXRVc2VyIiwiYm9keSIsImRvbWFpbk5hbWUiLCJzaXRlSWQiLCJ1c2VySWQiLCJpZCIsInNpdGUiLCJzaXRlRXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJzaW5nbGUiLCJvcmlnaW5hbFNpdGVOYW1lIiwic2l0ZV9uYW1lIiwiZG9tYWluRXJyb3IiLCJjbmFtZVJlc3VsdCIsImNuYW1lRXJyb3IiLCJkb21haW5VcGRhdGVFcnJvciIsInVwZGF0ZSIsInNpdGVfaWQiLCJkbnNfY29uZmlndXJlZCIsImNuYW1lX3RhcmdldCIsInVwZGF0ZWRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzaXRlVXBkYXRlRXJyb3IiLCJvbGROYW1lIiwibmV3TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/domain-mapping/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdomain-mapping%2Froute&page=%2Fapi%2Fdomain-mapping%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdomain-mapping%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();